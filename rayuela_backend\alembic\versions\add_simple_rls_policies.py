"""add_simple_rls_policies

Revision ID: add_simple_rls_policies
Revises: 5410f56c34d5
Create Date: 2025-06-25 10:00:00.000000

"""
from typing import Sequence, Union
import logging

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_simple_rls_policies'
down_revision: Union[str, None] = '5410f56c34d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Core tables that definitely exist and need RLS
CORE_TABLES = [
    "products",
    "end_users", 
    "interactions",
    "api_keys",
]

def create_basic_tenant_setup():
    """Create basic tenant setup with minimal complexity."""
    logger.info("Creating basic tenant setup...")
    
    try:
        # Create app schema if it doesn't exist
        op.execute("CREATE SCHEMA IF NOT EXISTS app;")
        
        # Create simple tenant_id setting function
        op.execute("""
            CREATE OR REPLACE FUNCTION app.set_tenant_id()
            RETURNS void AS $$
            BEGIN
                -- Simple function to set tenant context
                PERFORM set_config('app.tenant_id', NULL, false);
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # Grant basic permissions
        op.execute("GRANT USAGE ON SCHEMA app TO PUBLIC;")
        op.execute("GRANT EXECUTE ON FUNCTION app.set_tenant_id() TO PUBLIC;")
        
        logger.info("Basic tenant setup completed successfully")
        
    except Exception as e:
        logger.warning(f"Non-critical error in tenant setup: {e}")
        # Don't fail the migration for non-critical setup issues

def enable_rls_safely(table_name: str):
    """Enable RLS on a table safely, only if it exists."""
    logger.info(f"Enabling RLS on table: {table_name}")
    
    try:
        # Check if table exists and enable RLS
        op.execute(f"""
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM information_schema.tables 
                          WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                    
                    -- Enable RLS
                    ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;
                    
                    -- Create simple policies (drop existing first)
                    DROP POLICY IF EXISTS {table_name}_tenant_policy ON {table_name};
                    
                    CREATE POLICY {table_name}_tenant_policy ON {table_name}
                    FOR ALL
                    USING (
                        account_id = COALESCE(
                            current_setting('app.tenant_id', true)::integer,
                            account_id
                        )
                    )
                    WITH CHECK (
                        account_id = COALESCE(
                            current_setting('app.tenant_id', true)::integer,
                            account_id
                        )
                    );
                    
                    RAISE NOTICE 'RLS enabled for table: %', '{table_name}';
                ELSE
                    RAISE NOTICE 'Table % does not exist, skipping RLS', '{table_name}';
                END IF;
            END
            $$;
        """)
        
    except Exception as e:
        logger.warning(f"Non-critical error enabling RLS for {table_name}: {e}")
        # Don't fail the migration for individual table issues

def upgrade() -> None:
    """Upgrade schema with simple RLS policies."""
    logger.info("Starting simple RLS policies migration...")
    
    try:
        # Step 1: Create basic tenant setup
        create_basic_tenant_setup()
        
        # Step 2: Enable RLS for core tables only
        for table_name in CORE_TABLES:
            enable_rls_safely(table_name)
        
        logger.info("✅ Simple RLS policies migration completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during RLS migration: {e}")
        # Log the error but don't fail the migration completely
        logger.info("⚠️ Migration completed with warnings")

def downgrade() -> None:
    """Downgrade schema by removing RLS policies."""
    logger.info("Starting RLS policies rollback...")
    
    try:
        # Remove RLS policies for core tables
        for table_name in CORE_TABLES:
            logger.info(f"Removing RLS from table: {table_name}")
            
            try:
                op.execute(f"""
                    DO $$
                    BEGIN
                        IF EXISTS (SELECT 1 FROM information_schema.tables 
                                  WHERE table_schema = 'public' AND table_name = '{table_name}') THEN
                            
                            -- Drop policy
                            DROP POLICY IF EXISTS {table_name}_tenant_policy ON {table_name};
                            
                            -- Disable RLS
                            ALTER TABLE {table_name} DISABLE ROW LEVEL SECURITY;
                            
                        END IF;
                    END
                    $$;
                """)
            except Exception as e:
                logger.warning(f"Non-critical error removing RLS from {table_name}: {e}")
        
        logger.info("✅ RLS policies rollback completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during RLS rollback: {e}")
        # Don't fail the rollback completely
        logger.info("⚠️ Rollback completed with warnings")
