#!/usr/bin/env python3
"""
Script de debug para investigar el problema con ALLOWED_ORIGINS
"""

import os
import sys

# Agregar src al path
sys.path.insert(0, '/app/src')

def debug_allowed_origins():
    """Debug del problema de ALLOWED_ORIGINS."""
    
    print("🔍 DEBUG ALLOWED_ORIGINS")
    print("=" * 50)
    
    # 1. Verificar variable de entorno directa
    allowed_origins_raw = os.getenv('ALLOWED_ORIGINS')
    print(f"1. Variable de entorno ALLOWED_ORIGINS:")
    print(f"   Valor: {repr(allowed_origins_raw)}")
    print(f"   Tipo: {type(allowed_origins_raw)}")
    print(f"   Longitud: {len(allowed_origins_raw) if allowed_origins_raw else 'None'}")
    
    if allowed_origins_raw:
        print(f"   Bytes: {allowed_origins_raw.encode('utf-8')}")
        print(f"   Hex: {allowed_origins_raw.encode('utf-8').hex()}")
    
    # 2. Probar el validador manualmente
    print(f"\n2. Probando validador manualmente:")
    try:
        from typing import Union, List
        from pydantic import field_validator
        
        def assemble_string_list(v: Union[str, List[str]]) -> Union[List[str], str]:
            """Copia del validador de config.py"""
            print(f"   Input: {repr(v)} (tipo: {type(v)})")
            
            if isinstance(v, str):
                if not v.startswith("["):
                    # Si solo hay un host (sin comas), retornarlo como lista de un elemento
                    if "," in v:
                        result = [i.strip() for i in v.split(",")]
                        print(f"   Resultado (con comas): {result}")
                        return result
                    else:
                        result = [v.strip()]
                        print(f"   Resultado (sin comas): {result}")
                        return result
                else:
                    # Si empieza con '[', intentar evaluar como lista
                    try:
                        import ast
                        result = ast.literal_eval(v)
                        print(f"   Resultado (literal_eval): {result}")
                        return result
                    except:
                        result = [v.strip()]
                        print(f"   Resultado (fallback): {result}")
                        return result
            else:
                print(f"   No es string, retornando tal como está: {v}")
                return v
        
        if allowed_origins_raw:
            processed = assemble_string_list(allowed_origins_raw)
            print(f"   ✅ Validador manual exitoso: {processed}")
        else:
            print(f"   ⚠️ No hay valor para procesar")
            
    except Exception as e:
        print(f"   ❌ Error en validador manual: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
    
    # 3. Probar importación de settings paso a paso
    print(f"\n3. Probando importación de settings:")
    try:
        print("   Importando pydantic...")
        from pydantic import BaseSettings
        print("   ✅ Pydantic importado")
        
        print("   Importando config...")
        from src.core.config import Settings
        print("   ✅ Clase Settings importada")
        
        print("   Creando instancia...")
        settings = Settings()
        print("   ❌ No debería llegar aquí si hay error")
        
    except Exception as e:
        print(f"   ❌ Error en importación: {e}")
        import traceback
        print(f"   Traceback completo:")
        print(traceback.format_exc())

if __name__ == "__main__":
    debug_allowed_origins()
