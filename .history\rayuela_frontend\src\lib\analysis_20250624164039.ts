/**
 * This file contains utility functions for analyzing metrics and generating recommendations
 * based on the data from the API. These functions are pure and can be tested independently
 * of the UI components.
 */

import { RecommendationPerformanceMetrics, ConfidenceMetrics, ModelMetrics } from '@/lib/api/recommendation-metrics';
import { UsageStats } from '@/lib/generated/rayuelaAPI';
import { AccountInfo, AccountResponse } from '@/lib/api';
import { ChecklistItem } from '@/types/checklist';
import React from 'react';
import { allRecommendationRules, RecommendationRule } from '@/lib/recommendationRules';
import { formatString, metricRecommendationsStrings } from '@/lib/constants';

// Types for metric recommendations
export interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'accuracy' | 'diversity' | 'confidence' | 'performance';
  icon: React.ReactNode;
  actions: string[];
  metrics: {
    name: string;
    value: number;
    target: number;
    unit: string;
  }[];
}

// Additional types for analysis functions
export interface RecommendationSection {
  section: string;
  title: string;
  description: string;
  recommendations: {
    title: string;
    description: string;
    type: string;
    priority: 'high' | 'medium' | 'low';
    actions: string[];
    metrics: {
      name: string;
      value: number;
      unit: string;
      target?: number;
    }[];
  }[];
}

export interface AnalysisInsight {
  type: string;
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  metrics: {
    name: string;
    value: number;
    unit: string;
  }[];
  actions: string[];
}

/**
 * Helper function to safely access strings from metricRecommendationsStrings
 */
function getMetricString(key: string): string {
  return (metricRecommendationsStrings as any)[key] ?? key;
}

/**
 * Helper functions to get specific rules from the array
 */
function getRuleById(rules: Record<string, unknown>[], id: string): Record<string, unknown> | undefined {
  return rules.find(rule => rule.id === id);
}

// Mock rule objects for functions that expect specific rule structures
const mockRules = {
  model_optimization: {
    title: 'METRIC_REC_MODEL_TITLE',
    description: 'METRIC_REC_MODEL_DESC',
    actions: ['METRIC_REC_MODEL_ACTION_1'],
    metrics: [{
      name: 'METRIC_MODEL_ACCURACY',
      value: 0,
      unit: '%'
    }]
  },
  category_confidence: {
    title: 'METRIC_REC_CATEGORIES_TITLE',
    description: 'METRIC_REC_CATEGORIES_DESC',
    actions: ['METRIC_REC_CATEGORIES_ACTION_1'],
    params: { threshold: 0.5 }
  },
  confidence_factors: {
    title: 'METRIC_REC_FACTOR_TITLE',
    description: 'METRIC_REC_FACTOR_DESC',
    params: { threshold: 0.5 },
    factors: {
      data_quality: {
        name: 'METRIC_FACTOR_DATA_QUALITY',
        actions: ['METRIC_REC_FACTOR_ACTION_1'],
        metrics: [{
          name: 'METRIC_FACTOR_SCORE',
          valueMultiplier: 100,
          unit: '%'
        }]
      }
    }
  },
  general_improvements: {
    title: 'METRIC_REC_GENERAL_TITLE',
    description: 'METRIC_REC_GENERAL_DESC',
    actions: ['METRIC_REC_GENERAL_ACTION_1'],
    metrics: [{
      name: 'METRIC_GENERAL_SCORE',
      value: 85,
      unit: '%'
    }]
  },
  usage_analysis: {
    high_usage: {
      title: 'METRIC_REC_USAGE_TITLE',
      description: 'METRIC_REC_USAGE_DESC',
      actions: ['METRIC_REC_USAGE_ACTION_1'],
      metrics: [{
        name: 'METRIC_API_CALLS',
        unit: 'calls'
      }]
    }
  }
};

/**
 * Gets a value from an object using a path array
 *
 * @param obj The object to get the value from
 * @param path The path to the value as an array of keys
 * @returns The value at the path or undefined if not found
 */
function getValueByPath(obj: unknown, path: string[]): unknown {
  return path.reduce((acc: unknown, key: string) => 
    (acc && typeof acc === 'object' && acc !== null && key in acc) ? 
      (acc as Record<string, unknown>)[key] : undefined, obj);
}

/**
 * Compares two values using the specified comparison operator
 *
 * @param value1 First value
 * @param value2 Second value
 * @param operator Comparison operator
 * @returns Result of the comparison
 */
function compareValues(value1: number, value2: number, operator: 'lt' | 'gt' | 'eq' | 'lte' | 'gte'): boolean {
  switch (operator) {
    case 'lt': return value1 < value2;
    case 'gt': return value1 > value2;
    case 'eq': return value1 === value2;
    case 'lte': return value1 <= value2;
    case 'gte': return value1 >= value2;
    default: return false;
  }
}

/**
 * Handles special logic for recommendation rules
 *
 * @param rule The rule with special logic
 * @param performanceData Performance metrics data
 * @param confidenceData Confidence metrics data
 * @param iconComponents Object containing icon components
 * @returns A recommendation object if the rule applies, null otherwise
 */
function handleSpecialLogic(
  rule: RecommendationRule,
  performanceData: RecommendationPerformanceMetrics,
  confidenceData: ConfidenceMetrics,
  iconComponents: Record<string, React.ElementType>
): Recommendation | null {
  if (!rule.specialLogic) return null;

  const { type, params } = rule.specialLogic;

  switch (type) {
    case 'avgConfidence': {
      if (!params?.paths || !params.threshold) return null;

      // Calculate average confidence across multiple paths
      const values = params.paths.map((path: string[]) => getValueByPath(confidenceData, path) || 0);
      const avgValue = values.reduce((sum: number, val: number) => sum + val, 0) / values.length;

      if (avgValue < params.threshold) {
        return {
          id: rule.id,
          title: formatString(getMetricString(rule.title), {}),
          description: formatString(getMetricString(rule.description), {}),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-red-500" }),
          actions: rule.actions.map(action => getMetricString(action)),
          metrics: rule.metrics.map(metric => ({
            name: formatString(getMetricString(metric.name), {}),
            value: avgValue * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'worstModel': {
      if (!params?.models || !params.threshold) return null;

      // Find the worst performing model
      const modelValues: Record<string, { value: number, name: string, actions: string[] }> = {};

      Object.entries(params.models).forEach(([modelKey, modelConfig]: [string, any]) => {
        const rawValue = getValueByPath(confidenceData, modelConfig.path);
        const value = typeof rawValue === 'number' ? rawValue : 0;
        modelValues[modelKey] = {
          value,
          name: (metricRecommendationsStrings as Record<string, string>)[modelConfig.name],
          actions: modelConfig.actions
        };
      });

      const worstModel = Object.entries(modelValues)
        .sort(([, a], [, b]) => a.value - b.value)[0];

      if (worstModel && worstModel[1].value < params.threshold) {
        const modelName = worstModel[1].name;

        return {
          id: `${rule.id}-${worstModel[0]}`,
          title: formatString((metricRecommendationsStrings as any)[rule.title], { model: modelName }),
          description: formatString((metricRecommendationsStrings as any)[rule.description], { model: modelName }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: worstModel[1].actions.map(action => (metricRecommendationsStrings as any)[action]),
          metrics: rule.metrics.map(metric => ({
            name: formatString((metricRecommendationsStrings as any)[metric.name], { model: modelName }),
            value: worstModel[1].value * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'lowConfidenceCategories': {
      if (!params?.threshold || !params.maxCategories) return null;

      if (Object.keys((confidenceData as any).category_confidence).length === 0) return null;

      const categories = Object.entries((confidenceData as any).category_confidence)
        .sort(([, a], [, b]) => (a as number) - (b as number))
        .slice(0, params.maxCategories);

      if ((categories[0][1] as number) < params.threshold) {
        const worstCategory = categories[0][0];

        return {
          id: rule.id,
          title: formatString((metricRecommendationsStrings as any)[rule.title], {}),
          description: formatString((metricRecommendationsStrings as any)[rule.description], { category: worstCategory }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: rule.actions.map(action =>
            formatString((metricRecommendationsStrings as any)[action], { category: worstCategory })
          ),
          metrics: categories.map(category => ({
            name: `Confianza en ${category[0]}`,
            value: (category[1] as number) * 100,
            target: params.threshold * 100,
            unit: '%'
          }))
        };
      }
      return null;
    }

    case 'lowestConfidenceFactor': {
      if (!params?.factors || !params.threshold) return null;

      const lowestFactor = Object.entries((confidenceData as any).confidence_factors)
        .sort(([, a], [, b]) => (a as number) - (b as number))[0];

      if ((lowestFactor[1] as number) < params.threshold) {
        const factorKey = lowestFactor[0];
        const factorConfig = params.factors[factorKey];

        if (!factorConfig) return null;

        const factorName = (metricRecommendationsStrings as any)[factorConfig.name];

        return {
          id: `${rule.id}-${factorKey}`,
          title: formatString(getMetricString(rule.title), { factor: factorName }),
          description: formatString(getMetricString(rule.description), { factor: factorName }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: factorConfig.actions.map((action: string) => getMetricString(action)),
          metrics: rule.metrics.map(metric => ({
            name: formatString(getMetricString(metric.name), { factor: factorName }),
            value: (lowestFactor[1] as number) * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'negativeTrend': {
      if (!params?.minDays || !params.threshold || !params.path) return null;

      const trendsData = getValueByPath(confidenceData, params.path) as any[];

      if (trendsData && Array.isArray(trendsData) && trendsData.length >= params.minDays) {
        const lastDays = trendsData.slice(-params.minDays);

        // Check if there's a negative trend
        if (lastDays[lastDays.length - 1]?.avg_confidence < lastDays[0]?.avg_confidence * params.threshold) {
          const changeValue = ((lastDays[lastDays.length - 1].avg_confidence / lastDays[0].avg_confidence) - 1) * 100;

          return {
            id: rule.id,
            title: formatString(getMetricString(rule.title), {}),
            description: formatString(getMetricString(rule.description), {}),
            priority: rule.priority,
            category: rule.category,
            icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-red-500" }),
            actions: rule.actions.map(action => getMetricString(action)),
            metrics: rule.metrics.map(metric => ({
              name: formatString(getMetricString(metric.name), {}),
              value: changeValue,
              target: metric.target,
              unit: metric.unit
            }))
          };
        }
      }
      return null;
    }

    default:
      return null;
  }
}

/**
 * Generates recommendations based on performance and confidence metrics
 *
 * @param performanceData Performance metrics data
 * @param confidenceData Confidence metrics data
 * @param iconComponents Object containing icon components to use in recommendations
 * @returns Array of recommendations
 */
export function generateMetricRecommendations(
  performanceData: RecommendationPerformanceMetrics,
  confidenceData: ConfidenceMetrics,
  iconComponents: Record<string, React.ElementType>
): Recommendation[] {
  const recommendations: Recommendation[] = [];

  // Process all rules
  allRecommendationRules.forEach(rule => {
    // Handle rules with special logic
    if (rule.specialLogic) {
      const specialRecommendation = handleSpecialLogic(rule, performanceData, confidenceData, iconComponents);
      if (specialRecommendation) {
        recommendations.push(specialRecommendation);
      }
      return;
    }

    // Handle standard rules
    const metricValue = getValueByPath(
      rule.metricPath[0] === 'summary' ? performanceData : confidenceData,
      rule.metricPath
    );

    if (metricValue !== undefined && typeof metricValue === 'number' && compareValues(metricValue, rule.threshold, rule.comparison)) {
      recommendations.push({
        id: rule.id,
        title: formatString(getMetricString(rule.title), {}),
        description: formatString(getMetricString(rule.description), {}),
        priority: rule.priority,
        category: rule.category,
        icon: React.createElement(
          iconComponents[rule.iconKey],
          {
            className: `h-5 w-5 ${rule.priority === 'high' ? 'text-red-500' :
              rule.priority === 'medium' ? 'text-amber-500' :
                'text-blue-500'
              }`
          }
        ),
        actions: rule.actions.map(action => getMetricString(action)),
        metrics: rule.metrics.map(metric => {
          const value = getValueByPath(
            metric.valuePath[0] === 'summary' ? performanceData : confidenceData,
            metric.valuePath
          );

          return {
            name: formatString(getMetricString(metric.name), {}),
            value: (value as number) * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          };
        })
      });
    }
  });

  // Sort recommendations by priority
  const priorityOrder = { high: 0, medium: 1, low: 2 };
  recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  return recommendations;
}

/**
 * Analyzes account and usage data to determine which checklist items should be marked as completed
 *
 * @param accountData Account information from the API
 * @param usageData Usage information from the API
 * @param checklistItems Current checklist items
 * @param apiKeyData API Key data from useApiKeys hook (optional)
 * @returns Updated checklist items with completion status
 */
export function analyzeChecklistCompletion(
  accountData: AccountInfo,
  usageData: UsageStats | any,
  checklistItems: ChecklistItem[],
  apiKeyData?: Record<string, unknown> | null // API Key data from useApiKeys hook
): {
  updatedItems: ChecklistItem[],
  isNewApiKey: boolean,
  hasApiKey: boolean,
  hasSentCatalogData: boolean,
  hasSentInteractionData: boolean,
  hasTrainedModel: boolean,
  hasMadeApiCalls: boolean
} {
  // Check if the user has an API Key - use apiKeyData if available, fallback to legacy method
  const hasApiKey = apiKeyData ? !!apiKeyData : false;

  // Check if the user has seen the PostModalHighlight
  const hasSeenPostModal = localStorage.getItem('seenPostModalHighlight') === 'true';

  // Check if the API Key is recent (less than 24 hours) - use apiKeyData if available
  let isNewApiKey = false;
  if (apiKeyData && apiKeyData.created_at && (typeof apiKeyData.created_at === 'string' || typeof apiKeyData.created_at === 'number')) {
    const keyCreatedAt = new Date(apiKeyData.created_at);
    const now = new Date();
    const hoursSinceCreation = (now.getTime() - keyCreatedAt.getTime()) / (1000 * 60 * 60);
    isNewApiKey = hoursSinceCreation < 24;
  }

  // Check if the user has sent catalog data (products)
  // Support both UsageStats and UsageSummaryResponse formats
  const storageUsed = usageData.storage_used ?? usageData.storage?.usedBytes ?? 0;
  const hasSentCatalogData = storageUsed > 0;

  // Check if the user has sent interaction data
  // Use storage as proxy for data ingestion
  const hasSentInteractionData = storageUsed > 0;

  // Check if the user has trained a model
  // Check training info if available (UsageSummaryResponse format)
  const hasTrainedModel = !!(usageData.training?.lastTrainingDate);

  // Check if the user has made API calls to the recommendations endpoint
  // Support both formats
  const apiCallsCount = usageData.api_calls_count ?? usageData.apiCalls?.used ?? 0;
  const hasMadeApiCalls = apiCallsCount > 0;

  // Update checklist items
  const updatedItems = checklistItems.map(item => {
    if (item.id === 'generate_key' && item.autoDetect) {
      // Mark as completed if the user has an API Key or has seen the PostModalHighlight
      return { ...item, completed: hasApiKey || hasSeenPostModal };
    }
    if (item.id === 'send_catalog_data' && item.autoDetect) {
      return { ...item, completed: hasSentCatalogData };
    }
    if (item.id === 'send_interaction_data' && item.autoDetect) {
      return { ...item, completed: hasSentInteractionData };
    }
    if (item.id === 'train_model' && item.autoDetect) {
      return { ...item, completed: hasTrainedModel };
    }
    if (item.id === 'first_recommendation' && item.autoDetect) {
      return { ...item, completed: hasMadeApiCalls };
    }
    return item;
  });

  return {
    updatedItems,
    isNewApiKey,
    hasApiKey,
    hasSentCatalogData,
    hasSentInteractionData,
    hasTrainedModel,
    hasMadeApiCalls
  };
}

/**
 * Creates a checklist state object for localStorage
 *
 * @param checklistItems Checklist items
 * @param hasApiKey Whether the user has an API Key
 * @param hasSeenPostModal Whether the user has seen the post-modal highlight
 * @param hasSentCatalogData Whether the user has sent catalog data
 * @param hasSentInteractionData Whether the user has sent interaction data
 * @param hasTrainedModel Whether the user has trained a model
 * @param hasMadeApiCalls Whether the user has made API calls
 * @returns Record of checklist item IDs and their completion status
 */
export function createChecklistState(
  checklistItems: ChecklistItem[],
  hasApiKey: boolean,
  hasSeenPostModal: boolean,
  hasSentCatalogData: boolean,
  hasSentInteractionData: boolean,
  hasTrainedModel: boolean,
  hasMadeApiCalls: boolean
): Record<string, boolean> {
  const checklistState: Record<string, boolean> = {};

  checklistItems.forEach(item => {
    if (item.id === 'generate_key' && item.autoDetect) {
      checklistState[item.id] = hasApiKey || hasSeenPostModal;
    } else if (item.id === 'send_catalog_data' && item.autoDetect) {
      checklistState[item.id] = hasSentCatalogData;
    } else if (item.id === 'send_interaction_data' && item.autoDetect) {
      checklistState[item.id] = hasSentInteractionData;
    } else if (item.id === 'train_model' && item.autoDetect) {
      checklistState[item.id] = hasTrainedModel;
    } else if (item.id === 'first_recommendation' && item.autoDetect) {
      checklistState[item.id] = hasMadeApiCalls;
    } else {
      checklistState[item.id] = item.completed;
    }
  });

  return checklistState;
}

/**
 * Determines if the checklist should be highlighted based on user actions
 *
 * @param hasSeenPostModal Whether the user has seen the post-modal highlight
 * @param isNewApiKey Whether the API Key is recent
 * @returns Whether the checklist should be highlighted
 */
export function shouldHighlightChecklist(
  hasSeenPostModal: boolean,
  isNewApiKey: boolean
): boolean {
  return hasSeenPostModal && !localStorage.getItem('checklistHighlighted');
}

// Type guards for data validation
interface ConfidenceData {
  category_confidence: Record<string, number>;
  confidence_factors: Record<string, number>;
  confidence_trends: {
    last_7_days: Array<{ date: string; confidence: number }>;
  };
}

function isConfidenceData(data: unknown): data is ConfidenceData {
  if (!data || typeof data !== 'object') return false;
  const d = data as Record<string, unknown>;
  const trends = d.confidence_trends as Record<string, unknown> | undefined;
  return (
    typeof d.category_confidence === 'object' &&
    typeof d.confidence_factors === 'object' &&
    trends !== undefined &&
    typeof trends === 'object' &&
    Array.isArray(trends.last_7_days)
  );
}

export function generateModelOptimizationRecommendations(
  models: ModelMetrics[],
  modelName: string
): RecommendationSection | null {
  if (!models.length) return null;

  // Find worst performing models for specific metrics
  const worstModel = models
    .map(model => [model, worstMetric(model)] as const)
    .filter(([, metric]) => metric !== null)
    .sort(([, a], [, b]) => (a?.score || 0) - (b?.score || 0))[0];

  if (!worstModel || !worstModel[1]) return null;

  const rule = mockRules.model_optimization;

  return {
    section: 'model_optimization',
    title: formatString(getMetricString(rule.title), { model: modelName }),
    description: formatString(getMetricString(rule.description), { model: modelName }),
    recommendations: [{
      title: formatString(getMetricString(rule.title), { model: modelName }),
      description: formatString(getMetricString(rule.description), { model: modelName }),
      type: 'optimization',
      priority: 'medium',
      metrics: worstModel[1].metrics.map((metric: { name: string; value: number; unit: string }) => ({
        name: formatString(getMetricString(metric.name), { model: modelName }),
        value: metric.value,
        unit: metric.unit
      })),
      actions: worstModel[1].actions.map((action: string) => getMetricString(action)),
    }]
  };
}

export function generateCategoryConfidenceRecommendations(
  confidenceData: unknown
): RecommendationSection | null {
  if (!isConfidenceData(confidenceData)) return null;

  if (Object.keys(confidenceData.category_confidence).length === 0) return null;

  const categories = Object.entries(confidenceData.category_confidence)
    .sort(([, a], [, b]) => (a as number) - (b as number));

  const params = mockRules.category_confidence.params;
  if ((categories[0][1] as number) < params.threshold) {
    const worstCategory = categories[0][0];
    const rule = mockRules.category_confidence;

    return {
      section: 'category_confidence',
      title: formatString(getMetricString(rule.title), {}),
      description: formatString(getMetricString(rule.description), { category: worstCategory }),
      recommendations: [{
        title: formatString(getMetricString(rule.title), {}),
        description: formatString(getMetricString(rule.description), { category: worstCategory }),
        type: 'data_quality',
        priority: 'medium',
        actions: rule.actions.map(action =>
          formatString(getMetricString(action), { category: worstCategory })
        ),
        metrics: categories.map(([category, confidence]) => ({
          name: category,
          value: (confidence as number) * 100,
          unit: '%'
        }))
      }]
    };
  }

  return null;
}

export function generateConfidenceFactorRecommendations(
  confidenceData: unknown
): RecommendationSection | null {
  if (!isConfidenceData(confidenceData)) return null;

  const lowestFactor = Object.entries(confidenceData.confidence_factors)
    .sort(([, a], [, b]) => (a as number) - (b as number))[0];

  if ((lowestFactor[1] as number) < mockRules.confidence_factors.params.threshold) {
    const factorConfig = (mockRules.confidence_factors.factors as any)[lowestFactor[0]] ?? mockRules.confidence_factors.factors.data_quality;
    if (factorConfig) {
      const factorName = getMetricString(factorConfig.name);
      const rule = mockRules.confidence_factors;

      return {
        section: 'confidence_factors',
        title: formatString(getMetricString(rule.title), { factor: factorName }),
        description: formatString(getMetricString(rule.description), { factor: factorName }),
        recommendations: [{
          title: formatString(getMetricString(rule.title), { factor: factorName }),
          description: formatString(getMetricString(rule.description), { factor: factorName }),
          type: 'data_quality',
          priority: 'medium',
          actions: factorConfig.actions.map((action: string) => getMetricString(action)),
          metrics: [{
            name: formatString(getMetricString(factorConfig.metrics[0].name), { factor: factorName }),
            value: (lowestFactor[1] as number) * (factorConfig.metrics[0].valueMultiplier || 1),
            unit: factorConfig.metrics[0].unit
          }]
        }]
      };
    }
  }

  return null;
}

export function generateGeneralRecommendations(): RecommendationSection {
  const rule = mockRules.general_improvements;

  return {
    section: 'general_improvements',
    title: formatString(getMetricString(rule.title), {}),
    description: formatString(getMetricString(rule.description), {}),
    recommendations: [{
      title: formatString(getMetricString(rule.title), {}),
      description: formatString(getMetricString(rule.description), {}),
      type: 'general',
      priority: 'low',
      metrics: rule.metrics.map((metric: { name: string; value: number; unit: string }) => ({
        name: formatString(getMetricString(metric.name), {}),
        value: metric.value,
        unit: metric.unit
      })),
      actions: rule.actions.map((action: string) => getMetricString(action)),
    }]
  };
}

function analyzeUsageGrowth(
  usageData: UsageStats
): AnalysisInsight[] {
  const insights: AnalysisInsight[] = [];

  // Análisis simplificado
  const apiCallsUsed = usageData.api_calls_count || 0;

  // Si el uso está creciendo, sugerir optimización
  if (apiCallsUsed > 5000) {
    insights.push({
      type: 'usage_pattern',
      severity: 'medium',
      title: formatString(getMetricString(mockRules.usage_analysis.high_usage.title), {}),
      description: formatString(getMetricString(mockRules.usage_analysis.high_usage.description), {}),
      metrics: [{
        name: formatString(getMetricString(mockRules.usage_analysis.high_usage.metrics[0].name), {}),
        value: apiCallsUsed,
        unit: mockRules.usage_analysis.high_usage.metrics[0].unit
      }],
      actions: mockRules.usage_analysis.high_usage.actions.map((action: string) => getMetricString(action))
    });
  }

  return insights;
}

/**
 * Función principal para analizar y generar recomendaciones
 */
export function generateRecommendations(
  usageData: UsageStats,
  accountData: AccountResponse,
  confidenceData: unknown,
  models: ModelMetrics[] = []
): {
  recommendations: RecommendationSection[];
  insights: AnalysisInsight[];
  summary: {
    totalRecommendations: number;
    highPriorityCount: number;
    mediumPriorityCount: number;
    lowPriorityCount: number;
  };
} {
  const recommendations: RecommendationSection[] = [];
  const insights: AnalysisInsight[] = [];

  // Analizar rendimiento de modelos
  const modelAnalysis = generateModelOptimizationRecommendations(models, 'modelo actual');
  if (modelAnalysis) recommendations.push(modelAnalysis);

  // Analizar confianza por categorías
  const categoryAnalysis = generateCategoryConfidenceRecommendations(confidenceData);
  if (categoryAnalysis) recommendations.push(categoryAnalysis);

  // Analizar factores de confianza
  const factorAnalysis = generateConfidenceFactorRecommendations(confidenceData);
  if (factorAnalysis) recommendations.push(factorAnalysis);

  // Agregar recomendaciones generales
  recommendations.push(generateGeneralRecommendations());

  // Generar insights de uso
  insights.push(...analyzeUsageGrowth(usageData));

  // Calcular resumen
  const allRecs = recommendations.flatMap(r => r.recommendations);
  const summary = {
    totalRecommendations: allRecs.length,
    highPriorityCount: allRecs.filter(r => r.priority === 'high').length,
    mediumPriorityCount: allRecs.filter(r => r.priority === 'medium').length,
    lowPriorityCount: allRecs.filter(r => r.priority === 'low').length,
  };

  return {
    recommendations,
    insights,
    summary
  };
}

/**
 * Mock function for worst metric analysis
 */
function worstMetric(model: ModelMetrics): { score: number; metrics: any[]; actions: any[] } | null {
  // This is a placeholder implementation
  return {
    score: 0.6, // Mock score below threshold
    metrics: [
      { name: 'METRIC_MODEL_ACCURACY', value: 0.6, unit: '%' }
    ],
    actions: ['METRIC_REC_TREND_ACTION_1']
  };
}

// Helper function to safely access object properties
function safeGet(obj: Record<string, unknown>, path: string): unknown {
  return path.split('.').reduce((current: unknown, key: string) => {
    return current && typeof current === 'object' && key in current 
      ? (current as Record<string, unknown>)[key] 
      : undefined;
  }, obj as unknown);
}

// Helper function to check if value is a valid number
function isValidNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

// Type guard for API response
function isApiResponse(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === 'object';
}

// Enhanced analysis functions with better typing
export function analyzeApiUsage(data: unknown): AnalysisResult {
  if (!isApiResponse(data)) {
    return {
      insights: [],
      recommendations: [],
      anomalies: [],
      score: 0
    };
  }

  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];
  
  // Safely access properties
  const apiCalls = safeGet(data, 'api_calls');
  const dailyAverage = safeGet(data, 'daily_average');
  const weeklyGrowth = safeGet(data, 'weekly_growth');
  
  if (isValidNumber(apiCalls) && apiCalls > 1000) {
    insights.push(`Alto volumen de llamadas: ${apiCalls.toLocaleString()}`);
  }
  
  if (isValidNumber(dailyAverage) && dailyAverage > 100) {
    recommendations.push('Considera optimizar la frecuencia de llamadas');
  }
  
  if (isValidNumber(weeklyGrowth) && weeklyGrowth > 0.2) {
    anomalies.push('Crecimiento semanal inusualmente alto');
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: calculateScore([apiCalls, dailyAverage, weeklyGrowth])
  };
}

// Helper to calculate score from various metrics
function calculateScore(metrics: unknown[]): number {
  const validMetrics = metrics.filter(isValidNumber);
  if (validMetrics.length === 0) return 0;
  
  const sum = validMetrics.reduce((acc, val) => acc + val, 0);
  return Math.min(100, Math.max(0, sum / validMetrics.length));
}

// Enhanced analyze functions with better typing
export function analyzeRecommendationMetrics(data: unknown): AnalysisResult {
  if (!isApiResponse(data)) {
    return {
      insights: [],
      recommendations: [],
      anomalies: [],
      score: 0
    };
  }

  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];

  const models = safeGet(data, 'models');
  if (Array.isArray(models)) {
    models.forEach((model, index) => {
      if (isApiResponse(model)) {
        const precision = safeGet(model, 'metrics.precision');
        const recall = safeGet(model, 'metrics.recall');
        const modelType = safeGet(model, 'model_type');
        
        if (isValidNumber(precision) && precision < 0.5) {
          recommendations.push(`Modelo ${modelType || index + 1}: Precisión baja (${(precision * 100).toFixed(1)}%)`);
        }
        
        if (isValidNumber(recall) && recall < 0.3) {
          anomalies.push(`Modelo ${modelType || index + 1}: Recall muy bajo`);
        }
      }
    });
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: calculateModelScore(models)
  };
}

// Helper to calculate model performance score
function calculateModelScore(models: unknown): number {
  if (!Array.isArray(models)) return 0;
  
  let totalScore = 0;
  let validModels = 0;
  
  models.forEach(model => {
    if (isApiResponse(model)) {
      const precision = safeGet(model, 'metrics.precision');
      const recall = safeGet(model, 'metrics.recall');
      
      if (isValidNumber(precision) && isValidNumber(recall)) {
        totalScore += (precision + recall) / 2;
        validModels++;
      }
    }
  });
  
  return validModels > 0 ? (totalScore / validModels) * 100 : 0;
}

// Enhanced analysis functions
export function analyzeStorageUsage(data: unknown): AnalysisResult {
  if (!isApiResponse(data)) {
    return {
      insights: [],
      recommendations: [],
      anomalies: [],
      score: 0
    };
  }

  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];

  const storage = safeGet(data, 'storage');
  const usagePercent = safeGet(data, 'usage_percent');
  const growthRate = safeGet(data, 'growth_rate');

  if (isValidNumber(storage) && storage > 1000000) {
    insights.push(`Almacenamiento alto: ${(storage / 1000000).toFixed(2)}MB`);
  }

  if (isValidNumber(usagePercent) && usagePercent > 80) {
    recommendations.push('Considera limpiar datos antiguos o aumentar el plan');
  }

  if (isValidNumber(growthRate) && growthRate > 0.15) {
    anomalies.push('Crecimiento de almacenamiento acelerado');
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: calculateStorageScore(usagePercent, growthRate)
  };
}

// Helper to calculate storage score
function calculateStorageScore(usagePercent: unknown, growthRate: unknown): number {
  let score = 100;
  
  if (isValidNumber(usagePercent)) {
    if (usagePercent > 90) score -= 40;
    else if (usagePercent > 75) score -= 20;
    else if (usagePercent > 50) score -= 10;
  }
  
  if (isValidNumber(growthRate)) {
    if (growthRate > 0.2) score -= 30;
    else if (growthRate > 0.1) score -= 15;
  }
  
  return Math.max(0, score);
}

// Enhanced comprehensive analysis
export function analyzeAccountHealth(accountData: unknown, usageData: unknown): AnalysisResult {
  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];

  // Analyze account data
  if (isApiResponse(accountData)) {
    const planType = safeGet(accountData, 'plan_type');
    const status = safeGet(accountData, 'status');
    const expirationDate = safeGet(accountData, 'expiration_date');

    if (typeof planType === 'string' && planType === 'free') {
      insights.push('Plan gratuito activo');
      recommendations.push('Considera actualizar a un plan premium para más funcionalidades');
    }

    if (typeof status === 'string' && status !== 'active') {
      anomalies.push(`Estado de cuenta: ${status}`);
    }

    if (typeof expirationDate === 'string') {
      const expDate = new Date(expirationDate);
      const daysUntilExpiration = Math.ceil((expDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiration < 7) {
        anomalies.push(`Plan expira en ${daysUntilExpiration} días`);
      } else if (daysUntilExpiration < 30) {
        recommendations.push(`Plan expira en ${daysUntilExpiration} días - considera renovar`);
      }
    }
  }

  // Analyze usage data
  if (isApiResponse(usageData)) {
    const apiCalls = safeGet(usageData, 'api_calls');
    const storage = safeGet(usageData, 'storage');
    const limits = safeGet(usageData, 'limits');

    if (isValidNumber(apiCalls) && isApiResponse(limits)) {
      const apiLimit = safeGet(limits, 'api_calls');
      if (isValidNumber(apiLimit)) {
        const usagePercent = (apiCalls / apiLimit) * 100;
        if (usagePercent > 90) {
          anomalies.push(`Uso de API al ${usagePercent.toFixed(1)}% del límite`);
        } else if (usagePercent > 75) {
          recommendations.push(`Uso de API al ${usagePercent.toFixed(1)}% del límite`);
        }
      }
    }

    if (isValidNumber(storage) && isApiResponse(limits)) {
      const storageLimit = safeGet(limits, 'storage');
      if (isValidNumber(storageLimit)) {
        const storagePercent = (storage / storageLimit) * 100;
        if (storagePercent > 85) {
          anomalies.push(`Almacenamiento al ${storagePercent.toFixed(1)}% del límite`);
        }
      }
    }
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: calculateAccountScore(accountData, usageData)
  };
}

// Helper to calculate account health score
function calculateAccountScore(accountData: unknown, usageData: unknown): number {
  let score = 100;

  if (isApiResponse(accountData)) {
    const status = safeGet(accountData, 'status');
    if (typeof status === 'string' && status !== 'active') {
      score -= 50;
    }
  }

  if (isApiResponse(usageData)) {
    const limits = safeGet(usageData, 'limits');
    if (isApiResponse(limits)) {
      const apiCalls = safeGet(usageData, 'api_calls');
      const storage = safeGet(usageData, 'storage');
      const apiLimit = safeGet(limits, 'api_calls');
      const storageLimit = safeGet(limits, 'storage');

      if (isValidNumber(apiCalls) && isValidNumber(apiLimit)) {
        const apiUsage = (apiCalls / apiLimit) * 100;
        if (apiUsage > 95) score -= 30;
        else if (apiUsage > 85) score -= 15;
      }

      if (isValidNumber(storage) && isValidNumber(storageLimit)) {
        const storageUsage = (storage / storageLimit) * 100;
        if (storageUsage > 95) score -= 30;
        else if (storageUsage > 85) score -= 15;
      }
    }
  }

  return Math.max(0, score);
}

// Enhanced pattern analysis
export function analyzeUsagePatterns(data: unknown): AnalysisResult {
  if (!isApiResponse(data)) {
    return {
      insights: [],
      recommendations: [],
      anomalies: [],
      score: 0
    };
  }

  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];

  const history = safeGet(data, 'usage_history');
  if (Array.isArray(history) && history.length > 0) {
    // Analyze trends
    let totalCalls = 0;
    let peakHour = -1;
    let maxCalls = 0;

    history.forEach((point, index) => {
      if (isApiResponse(point)) {
        const calls = safeGet(point, 'api_calls');
        if (isValidNumber(calls)) {
          totalCalls += calls;
          if (calls > maxCalls) {
            maxCalls = calls;
            peakHour = index;
          }
        }
      }
    });

    if (totalCalls > 0) {
      insights.push(`Total de llamadas: ${totalCalls.toLocaleString()}`);
      if (peakHour >= 0) {
        insights.push(`Hora pico: ${peakHour}:00`);
      }
    }

    // Detect patterns
    const weekdayUsage = analyzeWeekdayPattern(history);
    if (weekdayUsage.score > 0.7) {
      insights.push('Patrón de uso concentrado en días laborales');
    }

    // Detect anomalies
    const spikes = detectUsageSpikes(history);
    if (spikes.length > 0) {
      anomalies.push(`${spikes.length} picos de uso detectados`);
    }
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: calculatePatternScore(history)
  };
}

// Helper functions for pattern analysis
function analyzeWeekdayPattern(history: unknown): { score: number } {
  if (!Array.isArray(history)) return { score: 0 };
  
  // Simplified weekday analysis
  let weekdayTotal = 0;
  let weekendTotal = 0;
  let weekdayCount = 0;
  let weekendCount = 0;

  history.forEach((point, index) => {
    if (isApiResponse(point)) {
      const calls = safeGet(point, 'api_calls');
      if (isValidNumber(calls)) {
        const dayOfWeek = index % 7;
        if (dayOfWeek >= 1 && dayOfWeek <= 5) {
          weekdayTotal += calls;
          weekdayCount++;
        } else {
          weekendTotal += calls;
          weekendCount++;
        }
      }
    }
  });

  if (weekdayCount === 0 || weekendCount === 0) return { score: 0 };

  const weekdayAvg = weekdayTotal / weekdayCount;
  const weekendAvg = weekendTotal / weekendCount;
  const ratio = weekdayAvg / (weekdayAvg + weekendAvg);

  return { score: ratio };
}

function detectUsageSpikes(history: unknown): unknown[] {
  if (!Array.isArray(history)) return [];
  
  const spikes: unknown[] = [];
  let previousValue = 0;

  history.forEach((point, index) => {
    if (isApiResponse(point)) {
      const calls = safeGet(point, 'api_calls');
      if (isValidNumber(calls)) {
        if (index > 0 && calls > previousValue * 2) {
          spikes.push(point);
        }
        previousValue = calls;
      }
    }
  });

  return spikes;
}

function calculatePatternScore(history: unknown): number {
  if (!Array.isArray(history) || history.length === 0) return 0;
  
  let validPoints = 0;
  let totalCalls = 0;
  let consistency = 0;

  history.forEach(point => {
    if (isApiResponse(point)) {
      const calls = safeGet(point, 'api_calls');
      if (isValidNumber(calls)) {
        totalCalls += calls;
        validPoints++;
      }
    }
  });

  if (validPoints === 0) return 0;

  const average = totalCalls / validPoints;
  let variance = 0;

  history.forEach(point => {
    if (isApiResponse(point)) {
      const calls = safeGet(point, 'api_calls');
      if (isValidNumber(calls)) {
        variance += Math.pow(calls - average, 2);
      }
    }
  });

  const standardDeviation = Math.sqrt(variance / validPoints);
  consistency = average > 0 ? Math.max(0, 1 - (standardDeviation / average)) : 0;

  return consistency * 100;
}

// Enhanced model analysis with better typing
export function analyzeModelPerformance(modelData: unknown): AnalysisResult {
  if (!isApiResponse(modelData)) {
    return {
      insights: [],
      recommendations: [],
      anomalies: [],
      score: 0
    };
  }

  const insights: string[] = [];
  const recommendations: string[] = [];
  const anomalies: string[] = [];
  let bestScore = -1;

  const models = safeGet(modelData, 'models');
  if (Array.isArray(models)) {
    let bestModel: Record<string, unknown> | null = null;

    models.forEach(model => {
      if (isApiResponse(model)) {
        const modelType = safeGet(model, 'model_type');
        const metrics = safeGet(model, 'metrics');
        
        if (isApiResponse(metrics)) {
          const precision = safeGet(metrics, 'precision');
          const recall = safeGet(metrics, 'recall');
          const ndcg = safeGet(metrics, 'ndcg');

          if (isValidNumber(precision) && isValidNumber(recall) && isValidNumber(ndcg)) {
            const score = (precision + recall + ndcg) / 3;
            
            if (score > bestScore) {
              bestScore = score;
              bestModel = model;
            }

            // Generate insights based on performance
            if (precision < 0.5) {
              recommendations.push(`${modelType}: Mejorar precisión (${(precision * 100).toFixed(1)}%)`);
            }
            
            if (recall < 0.4) {
              recommendations.push(`${modelType}: Mejorar recall (${(recall * 100).toFixed(1)}%)`);
            }
            
            if (ndcg < 0.6) {
              anomalies.push(`${modelType}: NDCG bajo (${(ndcg * 100).toFixed(1)}%)`);
            }
          }
        }
      }
    });

    if (bestModel) {
      const bestModelType = safeGet(bestModel, 'model_type');
      insights.push(`Mejor modelo: ${bestModelType} (Score: ${(bestScore * 100).toFixed(1)}%)`);
    }
  }

  return {
    insights,
    recommendations,
    anomalies,
    score: bestScore > 0 ? bestScore * 100 : 0
  };
}

// Type definitions for analysis results
interface AnalysisResult {
  insights: string[];
  recommendations: string[];
  anomalies: string[];
  score: number;
}

// Export analysis result type
export type { AnalysisResult };
