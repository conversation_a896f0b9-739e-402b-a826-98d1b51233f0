import asyncio
from logging.config import fileConfig
import os
import sys

from sqlalchemy import pool, engine_from_config
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context


# --- A<PERSON><PERSON> src al PYTHONPATH ---
# Esto permite a Alembic encontrar tus modelos y configuración
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # Sube un nivel desde alembic/
sys.path.insert(0, project_root)
# ---------------------------------

# --- Importar tu Base y configuración ---
from src.db.base import Base  # Asegúrate que Base esté definida aquí
from src.core.config import settings  # Importa tu configuración

# Importa tus modelos para que Base.metadata los conozca
# Es crucial importar TODOS los archivos de modelos aquí
import src.db.models  # Importa todos los modelos definidos en __init__.py

# ---------------------------------------

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# --- Configurar la URL de la base de datos desde settings ---
# Sobrescribe la URL del .ini con la de tu objeto settings
config.set_main_option("sqlalchemy.url", settings.database_url)
# ----------------------------------------------------------

# --- Configuración de timeouts para migraciones ---
# IMPORTANTE: Configuración de timeouts para evitar bloqueos indefinidos en producción
# 
# Los timeouts se configuran según el entorno:
# - Desarrollo: Timeouts más largos para debugging
# - Producción: Timeouts razonables para evitar bloqueos indefinidos
#
# NOTA CRÍTICA: Desactivar timeouts (=0) puede causar bloqueos de BD indefinidos.
# Si una migración específica requiere más tiempo, considerar:
# 1. Dividir la migración en pasos más pequeños
# 2. Implementar estrategias zero-downtime
# 3. Ejecutar en horarios de bajo tráfico
def get_migration_timeouts():
    """
    Obtiene la configuración de timeouts según el entorno.
    
    Returns:
        dict: Configuración de timeouts para la migración
    """
    # Detectar si estamos en desarrollo basado en variables de entorno
    is_development = (
        os.getenv('ENVIRONMENT', '').lower() in ['development', 'dev', 'local'] or
        os.getenv('DEBUG', '').lower() == 'true' or
        'localhost' in settings.database_url or
        '127.0.0.1' in settings.database_url
    )
    
    if is_development:
        # Timeouts más largos para desarrollo y debugging
        return {
            'statement_timeout': '30min',  # 30 minutos para desarrollo
            'lock_timeout': '10min',       # 10 minutos para locks
            'idle_in_transaction_session_timeout': '20min'
        }
    else:
        # Timeouts más estrictos para producción
        return {
            'statement_timeout': '10min',  # 10 minutos máximo por statement
            'lock_timeout': '2min',        # 2 minutos máximo para locks
            'idle_in_transaction_session_timeout': '5min'
        }

# Obtener configuración de timeouts
MIGRATION_TIMEOUTS = get_migration_timeouts()

print(f"🔧 Configuración de timeouts para migraciones:")
for key, value in MIGRATION_TIMEOUTS.items():
    print(f"   {key}: {value}")
# ----------------------------------------------------------


# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online_sync() -> None:
    """Run migrations in 'online' mode using synchronous connection.
    
    This is more reliable for Cloud Build environments.
    
    SECURITY NOTE: Configurado con timeouts razonables para evitar 
    bloqueos indefinidos en producción.
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Override the URL to use psycopg2 instead of asyncpg for sync operations
    sync_url = settings.sqlalchemy_database_url.replace('postgresql+asyncpg://', 'postgresql://')
    configuration['sqlalchemy.url'] = sync_url
    
    # Construir string de opciones de conexión con timeouts configurados
    options_string = ' '.join([
        f'-c {key}={value}' 
        for key, value in MIGRATION_TIMEOUTS.items()
    ]) + ' -c application_name=alembic_migration'
    
    # Use synchronous engine for better reliability in Cloud Build
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'options': options_string
        }
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

async def run_migrations_online() -> None:
    """Run migrations in 'online' mode using async approach.

    This is a fallback when synchronous approach fails.
    
    SECURITY NOTE: Configurado con timeouts razonables para evitar 
    bloqueos indefinidos en producción.
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Preparar server_settings con timeouts configurados
    server_settings = {
        'jit': 'off',
        'application_name': 'alembic_migration',
        **MIGRATION_TIMEOUTS
    }
    
    # Use simplified configuration for better reliability in Cloud Build
    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'server_settings': server_settings,
            'command_timeout': 600  # 10 minutos de timeout para comandos
        }
    )

    try:
        # Create connection with extended timeout for Cloud Build environment
        # Aumentar timeout de conexión pero mantenerlo razonable
        connection = await asyncio.wait_for(connectable.connect(), timeout=900)  # 15 minutos
        
        try:
            await connection.run_sync(do_run_migrations)
        finally:
            await connection.close()
    except asyncio.TimeoutError:
        print("❌ Migration connection timed out after 900 seconds")
        print("💡 Si la migración es legítimamente larga, considerar:")
        print("   1. Dividir en migraciones más pequeñas")
        print("   2. Ejecutar en horarios de bajo tráfico")
        print("   3. Implementar estrategias zero-downtime")
        raise
    finally:
        await connectable.dispose()


if context.is_offline_mode():
    run_migrations_offline()
else:
    # Use synchronous approach for better reliability in Cloud Build
    try:
        run_migrations_online_sync()
    except Exception as e:
        print(f"⚠️ Synchronous migration failed: {e}")
        print("🔄 Falling back to async approach...")
        asyncio.run(run_migrations_online())
