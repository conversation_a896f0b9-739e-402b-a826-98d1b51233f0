"use client";

import { useState, useEffect } from 'react';
import { format, parseISO, subDays } from 'date-fns';
import {
  useAuth,
  useAccountInfo,
  useUsageSummary,
  useUsageHistory
} from '@/lib/hooks';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { toast } from 'sonner';
import { handleApiError } from '@/lib/error-handler';
import {
  BarChart3Icon,
  DatabaseIcon,
  RefreshCwIcon,
  ClockIcon,
  TrendingUpIcon,
  InfoIcon,
  AlertCircleIcon,
  HelpCircleIcon
} from 'lucide-react';
import { SemanticIcon } from '@/components/ui/icon';
import { BillingButton } from '@/components/dashboard/BillingButton';
import { BillingPortalButton } from '@/components/dashboard/BillingPortalButton';
import UsageChart from '@/components/dashboard/UsageChart';
import { DateRangeSelector, DateRange } from '@/components/dashboard/DateRangeSelector';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

import { formatBytes, formatNumber } from '@/lib/utils/format';

// Interfaz para los datos de uso históricos
interface UsageDataPoint {
  date: string;
  apiCalls: number;
  storage: number;
}

export default function UsageDashboard() {
  useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filteredData, setFilteredData] = useState<UsageDataPoint[]>([]);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  // Usar los hooks personalizados para obtener datos

  // Obtener información de la cuenta
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading,
  } = useAccountInfo();

  // Obtener datos de uso
  const {
    usageData: usageSummary,
    error: usageError,
    isLoading: isUsageLoading,
    mutate: mutateUsage
  } = useUsageSummary();

  // Obtener historial de uso
  const {
    data: historyData,
    error: historyError,
    isLoading: isHistoryLoading,
    mutate: mutateHistory
  } = useUsageHistory(
    dateRange.from,
    dateRange.to
  );

  // Convertir los datos históricos al formato esperado por el componente UsageChart
  useEffect(() => {
    if (historyData) {
      const chartData: UsageDataPoint[] = historyData.map(item => {
        const typedItem = item as { date: string; api_calls: number; storage: number };
        return {
          date: typedItem.date,
          apiCalls: typedItem.api_calls,
          storage: typedItem.storage
        };
      });
      setFilteredData(chartData);
    }
  }, [historyData]);

  // Función para obtener la última actualización de datos
  const getLastUpdateTime = (): string => {
    return 'Hace unos momentos';
  };

  // Función para refrescar los datos
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([mutateUsage(), mutateHistory()]);
      toast.success('Datos de uso actualizados');
    } catch (error: unknown) {
      handleApiError(error, 'Error al actualizar los datos de uso');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Función para manejar el cambio de rango de fechas
  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };

  // Calcular totales para el rango seleccionado
  const calculateTotals = () => {
    if (!filteredData.length) return { apiCalls: 0, storage: 0 };

    // Para las llamadas a la API, sumamos todas las llamadas en el rango
    const totalApiCalls = filteredData.reduce((sum, item) => sum + item.apiCalls, 0);

    // Para el almacenamiento, tomamos el valor más reciente
    const latestStorage = filteredData[filteredData.length - 1]?.storage || 0;

    return { apiCalls: totalApiCalls, storage: latestStorage };
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const totals = calculateTotals();

  // Estado de carga combinado
  const isLoading = isUsageLoading || isAccountLoading || isHistoryLoading;

  // Mostrar un mensaje de carga mientras se obtienen los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-display">Uso de API</h1>
          <Skeleton className="h-9 w-24" />
        </div>

        {/* Skeletons para las tarjetas de métricas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-4 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mx-auto mb-2" />
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Skeleton para el gráfico */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Función para verificar errores críticos
  const hasCriticalErrors = (): boolean => {
    return !!(usageError || accountError || historyError);
  };

  // Función para verificar si hay datos disponibles
  const hasUsageData = (): boolean => {
    return !!(usageSummary && (usageSummary.apiCalls?.used || usageSummary.storage?.usedBytes));
  };

  // Mostrar errores críticos
  if (hasCriticalErrors()) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-display">Uso de API</h1>
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="h-9"
          >
            <RefreshCwIcon className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
        </div>

        <Alert variant="destructive" className="mb-6">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Error al cargar datos</AlertTitle>
          <AlertDescription>
                    {(typeof usageError === 'string' ? usageError : usageError?.message) || 
         (typeof accountError === 'string' ? accountError : accountError?.message) || 
         (typeof historyError === 'string' ? historyError : historyError?.message) || 
         'Ocurrió un error al cargar los datos de uso.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* Header con título y botón de actualizar */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-display mb-2">Uso de API</h1>
          <p className="text-muted-foreground">
            Monitorea el uso de tu API y almacenamiento en tiempo real
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="h-9"
          >
            <RefreshCwIcon className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Selector de rango de fechas */}
      <div className="mb-6">
        <DateRangeSelector
          onChange={handleDateRangeChange}
          className="w-auto"
        />
      </div>

      {/* Información de última actualización */}
      <div className="mb-6 flex items-center text-sm text-muted-foreground">
        <SemanticIcon icon={ClockIcon} size="sm" context="muted" className="mr-1" />
        <span>Última actualización: {getLastUpdateTime()}</span>
      </div>

      {/* Tarjetas de métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Tarjeta de Llamadas a la API */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={BarChart3Icon} size="md" context="primary" className="mr-2" />
              Llamadas a la API
            </CardTitle>
            <CardDescription>
              Total en el periodo: {dateRange.from ? format(dateRange.from, 'dd/MM/yy') : 'inicio'} - {dateRange.to ? format(dateRange.to, 'dd/MM/yy') : 'fin'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-metric text-center py-2 text-primary">
              {formatNumber(usageSummary?.apiCalls?.used || 0)}
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Uso actual</span>
                <span className="font-medium">{formatNumber(usageSummary?.apiCalls?.used || 0)}</span>
              </div>
              <Progress
                value={Math.min(((usageSummary?.apiCalls?.used || 0) / (usageSummary?.apiCalls?.limit || 10000)) * 100, 100)}
                className="h-1.5"
              />
              <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
                <div className="flex items-center">
                  <SemanticIcon icon={ClockIcon} size="xs" context="muted" className="mr-1" />
                  <span>Próximo reset:</span>
                </div>
                <span className="font-medium">{usageSummary?.apiCalls?.resetDate ? format(parseISO(usageSummary.apiCalls.resetDate), 'dd/MM/yy HH:mm') : 'No disponible'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tarjeta de Almacenamiento */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={DatabaseIcon} size="md" context="primary" className="mr-2" />
              Almacenamiento
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SemanticIcon icon={HelpCircleIcon} size="sm" context="muted" className="ml-1 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Datos almacenados en tu cuenta</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardTitle>
            <CardDescription>
              Espacio utilizado al {dateRange.to ? format(dateRange.to, 'dd/MM/yy') : 'final del periodo'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-metric text-center py-2 text-success">
              {formatBytes(usageSummary?.storage?.usedBytes || 0)}
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Uso actual</span>
                <div className="flex items-center">
                  <span className="font-medium">{formatBytes(usageSummary?.storage?.usedBytes || 0)}</span>
                  <span className="ml-1">/ {usageSummary?.storage?.limitBytes ? formatBytes(usageSummary.storage.limitBytes) : '∞'}</span>
                </div>
              </div>
              <Progress
                value={usageSummary?.storage?.percentage || 0}
                className="h-1.5"
              />
              <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
                <div className="flex items-center">
                  <SemanticIcon icon={ClockIcon} size="xs" context="muted" className="mr-1" />
                  <span>Última medición:</span>
                </div>
                <span className="font-medium">{usageSummary?.storage?.lastMeasured ? format(parseISO(usageSummary.storage.lastMeasured), 'dd/MM/yy HH:mm') : 'No disponible'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tarjeta de Estado de la Cuenta */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={InfoIcon} size="md" context="primary" className="mr-2" />
              Estado de la Cuenta
            </CardTitle>
            <CardDescription>
              Información general de tu cuenta
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-3 py-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">
                  <SemanticIcon icon={InfoIcon} size="sm" context="primary" className="mr-1.5" />
                  <span className="text-muted-foreground">Plan actual:</span>
                </div>
                                 <Badge variant="default" className="text-xs">
                   {(accountData as { plan?: string })?.plan || 'Básico'}
                 </Badge>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm">
                  <SemanticIcon icon={DatabaseIcon} size="sm" context="primary" className="mr-1.5" />
                  <span className="text-muted-foreground">Datos disponibles:</span>
                </div>
                <span className="font-medium text-sm">
                  {hasUsageData() ? 'Sí' : 'No'}
                </span>
              </div>
            </div>

            {!hasUsageData() && (
              <div className="flex flex-col items-center justify-center py-4 text-center">
                <SemanticIcon icon={InfoIcon} size="xl" context="muted" className="mb-2" />
                <p className="text-muted-foreground font-medium text-sm">No hay datos disponibles</p>
                <p className="text-xs text-muted-foreground text-center max-w-md">
                  Los datos de uso se mostrarán aquí una vez que comiences a usar la API.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Mostrar componente de gráfico */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <SemanticIcon icon={TrendingUpIcon} size="md" context="primary" className="mr-2" />
            Historial de Uso
          </CardTitle>
          <CardDescription>
            Tendencias de uso de la API durante el periodo seleccionado
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Mensaje informativo */}
          <div className="mb-4 p-3 bg-info-light rounded-lg border border-info/20">
            <p className="text-sm flex items-start">
              <SemanticIcon icon={InfoIcon} size="md" context="info" className="mr-2 shrink-0 mt-0.5" />
              <span>
                El gráfico muestra las tendencias de uso de tu API durante el periodo seleccionado.
                Los datos se actualizan automáticamente y reflejan las llamadas realizadas y el almacenamiento utilizado.
              </span>
            </p>
          </div>

          <UsageChart
            data={filteredData}
            isLoading={isHistoryLoading}
            error={historyError}
          />
        </CardContent>
      </Card>

      {/* Tarjetas de facturación */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <BillingButton priceId="default_price_id" planName="Pro" />
        <BillingPortalButton />
      </div>
    </div>
  );
}
