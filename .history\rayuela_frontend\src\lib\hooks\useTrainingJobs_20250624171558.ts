import { useState, useEffect } from 'react';
import { getRayuela } from '@/lib/generated/rayuelaAPI';

export interface TrainingJob {
  id: number;
  model_name: string;
  model_version: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration?: number;
  error_message?: string;
  parameters?: Record<string, number | string>;
  metrics?: Record<string, number>;
  task_id?: string;
}

export function useTrainingJobs() {
  const [jobs, setJobs] = useState<TrainingJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Implement training jobs listing endpoint
      try {
        // const response = await getRayuela().listTrainingJobsApiV1PipelineJobsGet();
        // const jobsList = response.data;
        const jobsList: unknown[] = [];
        
        const jobsData: TrainingJob[] = jobsList.map((jobStatus: unknown) => {
          // Type assertion for API response structure
          const apiJobStatus = jobStatus as {
            job_id: number;
            model?: { artifact_name?: string; artifact_version?: string };
            status: string;
            created_at: string;
            started_at?: string;
            completed_at?: string;
            error_message?: string;
            task_id?: string;
            parameters?: Record<string, unknown>;
            metrics?: Record<string, unknown>;
          };

          const job: TrainingJob = {
            id: apiJobStatus.job_id,
            model_name: apiJobStatus.model?.artifact_name || 'Recommendation Model',
            model_version: apiJobStatus.model?.artifact_version || 'v1.0',
            status: apiJobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            created_at: apiJobStatus.created_at,
            started_at: apiJobStatus.started_at || undefined,
            completed_at: apiJobStatus.completed_at || undefined,
            error_message: apiJobStatus.error_message || undefined,
            task_id: apiJobStatus.task_id || undefined,
            parameters: apiJobStatus.parameters ? 
              Object.fromEntries(
                Object.entries(apiJobStatus.parameters).filter(([, value]) =>
                  typeof value === 'number' || typeof value === 'string'
                )
              ) as Record<string, number | string> : undefined,
            metrics: apiJobStatus.metrics ? 
              Object.fromEntries(
                Object.entries(apiJobStatus.metrics).filter(([, value]) =>
                  typeof value === 'number'
                )
              ) as Record<string, number> : undefined,
          };
          
          // Calculate duration if both started_at and completed_at are available
          if (job.started_at && job.completed_at) {
            const startTime = new Date(job.started_at).getTime();
            const endTime = new Date(job.completed_at).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          return job;
        });
        
        setJobs(jobsData);
        return; // Exit early if successful
      } catch (listingError) {
        console.warn('Listing endpoint not available, falling back to localStorage approach:', listingError);
      }
      
      // Fallback: Get training jobs from stored job IDs since there's no dedicated listing endpoint
      const storedJobIds = localStorage.getItem('trainingJobIds');
      const jobIds: number[] = storedJobIds ? JSON.parse(storedJobIds) : [];
      
      const jobsData: TrainingJob[] = [];
      
      // Fetch status for each known job ID
      for (const jobId of jobIds) {
        try {
          const response = await getRayuela().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(jobId);
          const jobStatus = response.data;
          
          // Transform the backend response to match our interface
          const job: TrainingJob = {
            id: jobStatus.job_id,
            model_name: jobStatus.model?.artifact_name || 'Recommendation Model',
            model_version: jobStatus.model?.artifact_version || 'v1.0',
            status: jobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            created_at: jobStatus.created_at,
            started_at: jobStatus.started_at || undefined,
            completed_at: jobStatus.completed_at || undefined,
            error_message: jobStatus.error_message || undefined,
            task_id: jobStatus.task_id || undefined,
            parameters: jobStatus.parameters ? 
              Object.fromEntries(
                Object.entries(jobStatus.parameters).filter(([, value]) =>
                  typeof value === 'number' || typeof value === 'string'
                )
              ) as Record<string, number | string> : undefined,
            metrics: jobStatus.metrics ? 
              Object.fromEntries(
                Object.entries(jobStatus.metrics).filter(([, value]) =>
                  typeof value === 'number'
                )
              ) as Record<string, number> : undefined,
          };
          
          // Calculate duration if both started_at and completed_at are available
          if (job.started_at && job.completed_at) {
            const startTime = new Date(job.started_at).getTime();
            const endTime = new Date(job.completed_at).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          jobsData.push(job);
        } catch (jobError) {
          console.warn(`Error fetching training job ${jobId}:`, jobError);
          // Continue with other jobs even if one fails
        }
      }
      
      // Sort jobs by creation date (newest first)
      jobsData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setJobs(jobsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading training jobs');
      console.error('Error loading training jobs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getJobStatus = async (jobId: number) => {
    try {
      const response = await getRayuela().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(jobId);
      return response.data;
    } catch (err) {
      console.error('Error fetching training job status:', err);
      throw err;
    }
  };

  const startTraining = async (parameters?: Record<string, unknown>) => {
    try {
      const response = await getRayuela().trainModelsApiV1PipelineTrainPost();
      
      // Store the new job ID for future retrieval
      const jobId = response.data.job_id;
      if (jobId) {
        const storedJobIds = localStorage.getItem('trainingJobIds');
        const jobIds: number[] = storedJobIds ? JSON.parse(storedJobIds) : [];
        jobIds.unshift(jobId); // Add to beginning of array
        
        // Keep only the last 20 job IDs to avoid localStorage bloat
        const limitedJobIds = jobIds.slice(0, 20);
        localStorage.setItem('trainingJobIds', JSON.stringify(limitedJobIds));
      }
      
      // Refresh jobs list after starting new training
      await fetchJobs();
      return response.data;
    } catch (err) {
      console.error('Error starting training:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return {
    jobs,
    isLoading,
    error,
    fetchJobs,
    getJobStatus,
    startTraining
  };
} 