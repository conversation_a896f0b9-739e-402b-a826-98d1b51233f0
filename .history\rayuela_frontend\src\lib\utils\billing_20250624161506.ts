/**
 * Utility functions for billing and plan management
 */

import { AccountResponse, PlanInfo } from '@/lib/generated/rayuelaAPI';

/**
 * Format bytes to human-readable format
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format numbers with thousands separators
 */
export function formatNumber(num: number): string {
  if (num === -1) return 'Ilimitado';
  return new Intl.NumberFormat().format(num);
}

/**
 * Format percentage with specified decimals
 */
export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Get the current plan from account data
 */
export function getCurrentPlan(
  accountData?: AccountResponse | null
): string | null {
  if (!accountData) return null;

  // Handle both AccountResponse and AccountInfo types
  if ('subscription' in accountData && accountData.subscription) {
    return accountData.subscription.plan || null;
  }

  return null;
}

/**
 * Get plan display name with proper formatting
 */
export function getPlanDisplayName(planId: string): string {
  const planNames: Record<string, string> = {
    'FREE': 'Developer Sandbox',
    'STARTER': 'Starter',
    'PRO': 'Pro',
    'ENTERPRISE': 'Enterprise'
  };

  return planNames[planId] || planId;
}

/**
 * Determine the action type for a plan button
 */
export function getPlanActionType(
  planId: string,
  currentPlan?: string | null
): 'upgrade' | 'downgrade' | 'contact' | 'subscribe' | 'current' {
  if (!currentPlan) return 'subscribe';
  if (planId === currentPlan) return 'current';
  if (planId === 'ENTERPRISE') return 'contact';

  // Define plan hierarchy for upgrade/downgrade logic
  const planHierarchy = ['FREE', 'STARTER', 'PRO', 'ENTERPRISE'];
  const currentIndex = planHierarchy.indexOf(currentPlan);
  const targetIndex = planHierarchy.indexOf(planId);

  if (currentIndex === -1 || targetIndex === -1) return 'subscribe';

  return targetIndex > currentIndex ? 'upgrade' : 'downgrade';
}

/**
 * Get the appropriate button variant for a plan
 */
export function getPlanButtonVariant(
  planId: string,
  currentPlan?: string | null,
  isRecommended?: boolean
): 'default' | 'outline' | 'secondary' {
  if (planId === currentPlan) return 'secondary';
  if (isRecommended) return 'default';
  return 'outline';
}

/**
 * Check if a plan is an upgrade from the current plan
 */
export function isPlanUpgrade(planId: string, currentPlan?: string | null): boolean {
  return getPlanActionType(planId, currentPlan) === 'upgrade';
}

/**
 * Check if a plan is a downgrade from the current plan
 */
export function isPlanDowngrade(planId: string, currentPlan?: string | null): boolean {
  return getPlanActionType(planId, currentPlan) === 'downgrade';
}

/**
 * Get usage percentage for a metric
 */
export function getUsagePercentage(used: number, limit: number): number {
  if (limit === -1 || limit === 0) return 0; // Unlimited or no limit
  return Math.min((used / limit) * 100, 100);
}

/**
 * Get usage status color based on percentage
 */
export function getUsageStatusColor(percentage: number): string {
  if (percentage >= 90) return 'text-red-600 dark:text-red-400';
  if (percentage >= 75) return 'text-yellow-600 dark:text-yellow-400';
  return 'text-green-600 dark:text-green-400';
}

/**
 * Get progress bar color based on percentage
 */
export function getProgressBarColor(percentage: number): string {
  if (percentage >= 90) return 'bg-red-500';
  if (percentage >= 75) return 'bg-yellow-500';
  return 'bg-blue-500';
}

/**
 * Format renewal date
 */
export function formatRenewalDate(dateString?: string | null): string {
  if (!dateString) return 'Sin fecha de renovación';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return 'Fecha inválida';
  }
}

/**
 * Get plan features formatted for display
 */
export function getFormattedPlanFeatures(plan: PlanInfo): string[] {
  const features = [...plan.features];

  // Add limit-based features
  const limits = plan.limits as Record<string, unknown>;
  if (limits.api_calls && typeof limits.api_calls === 'number' && limits.api_calls > 0) {
    features.unshift(`${formatNumber(limits.api_calls)} llamadas API/mes`);
  } else {
    features.unshift('Llamadas API ilimitadas');
  }

  if (limits.storage_gb && typeof limits.storage_gb === 'number' && limits.storage_gb > 0) {
    features.unshift(`${limits.storage_gb} GB de almacenamiento`);
  } else if (limits.storage_mb && typeof limits.storage_mb === 'number' && limits.storage_mb > 0) {
    features.unshift(`${limits.storage_mb} MB de almacenamiento`);
  } else {
    features.unshift('Almacenamiento ilimitado');
  }

  return features;
}

/**
 * Check if a plan has unlimited resources
 */
export function isPlanUnlimited(plan: PlanInfo): boolean {
  return plan.limits.api_calls === -1 || plan.limits.storage_bytes === -1;
}

/**
 * Get plan price display
 */
export function getPlanPriceDisplay(plan: PlanInfo): string {
  if (plan.contact_required) return 'Contactar';
  return plan.price;
}

/**
 * Format currency values for display
 */
export function formatCurrency(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return 'N/A';

  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2
  }).format(numValue);
}

/**
 * Format usage metric names for display
 */
export function formatUsageMetric(metricKey: string): string {
  const metricNames: Record<string, string> = {
    'api_calls': 'Llamadas API',
    'storage_bytes': 'Almacenamiento',
    'storage_mb': 'Almacenamiento (MB)',
    'storage_gb': 'Almacenamiento (GB)',
    'monthly_api_calls': 'Llamadas API mensuales',
    'monthly_storage': 'Almacenamiento mensual',
    'documents': 'Documentos',
    'users': 'Usuarios'
  };

  return metricNames[metricKey] || metricKey;
}

/**
 * Sort plans by hierarchy (FREE -> STARTER -> PRO -> ENTERPRISE)
 */
export function sortPlansByHierarchy(plans: Record<string, PlanInfo>): [string, PlanInfo][] {
  const hierarchy = ['FREE', 'STARTER', 'PRO', 'ENTERPRISE'];

  return Object.entries(plans).sort(([a], [b]) => {
    const indexA = hierarchy.indexOf(a);
    const indexB = hierarchy.indexOf(b);
    return indexA - indexB;
  });
}

// Interfaz para información de la cuenta
interface AccountInfo {
  name: string;
  email: string;
  plan: string;
  subscription?: {
    status: string;
    expiresAt?: string;
  };
}

// Mock function to calculate billing status
export function calculateBillingStatus(account: AccountInfo): {
  status: 'active' | 'expired' | 'trial' | 'inactive';
  daysRemaining?: number;
  planName: string;
} {
  return {
    status: 'active',
    planName: account.plan || 'Free',
  };
}

// Mock function to format plan names
export function formatPlanName(planId: string): string {
  const planNames: Record<string, string> = {
    'free': 'Plan Gratuito',
    'basic': 'Plan Básico',
    'pro': 'Plan Pro',
    'enterprise': 'Plan Enterprise'
  };
  
  return planNames[planId] || planId;
}

// Mock function to check if upgrade is available
export function canUpgrade(currentPlan: string): boolean {
  const hierarchy = ['free', 'basic', 'pro', 'enterprise'];
  const currentIndex = hierarchy.indexOf(currentPlan.toLowerCase());
  return currentIndex < hierarchy.length - 1;
}
