"""consolidated_initial_migration

Revision ID: 20250625_130000
Revises: 
Create Date: 2025-06-25 13:00:00.000000

Migración consolidada que incluye todas las tablas, índices, RLS policies y constraints
necesarios para el deployment inicial de Rayuela.
"""
from typing import Sequence, Union
import logging

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '20250625_130000'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def upgrade() -> None:
    """Upgrade schema with complete consolidated migration."""
    logger.info("Starting consolidated initial migration...")
    
    try:
        # Create necessary extensions
        logger.info("Creating PostgreSQL extensions...")
        op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
        op.execute("CREATE EXTENSION IF NOT EXISTS btree_gin;")
        
        # Create app schema for tenant functions
        logger.info("Creating app schema...")
        op.execute("CREATE SCHEMA IF NOT EXISTS app;")
        
        # Create accounts table (base table)
        logger.info("Creating accounts table...")
        op.create_table('accounts',
            sa.Column('account_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('name', sa.String(), nullable=False),
            sa.Column('mercadopago_customer_id', sa.String(length=255), nullable=True, comment='Mercado Pago Customer ID'),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.Column('onboarding_checklist_status', postgresql.JSON(), nullable=True, comment='JSON object storing the status of onboarding checklist items'),
            sa.PrimaryKeyConstraint('account_id')
        )
        op.create_index(op.f('ix_accounts_mercadopago_customer_id'), 'accounts', ['mercadopago_customer_id'], unique=False)
        
        # Create api_keys table
        logger.info("Creating api_keys table...")
        op.create_table('api_keys',
            sa.Column('api_key_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(length=255), nullable=False),
            sa.Column('api_key_hash', sa.String(length=255), nullable=False),
            sa.Column('api_key_prefix', sa.String(length=10), nullable=False),
            sa.Column('api_key_last_chars', sa.String(length=4), nullable=False),
            sa.Column('permissions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('last_used', sa.DateTime(timezone=True), nullable=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('api_key_id'),
            sa.UniqueConstraint('api_key_hash', name='uq_api_keys_hash_global')
        )
        op.create_index('idx_api_key_account', 'api_keys', ['account_id'], unique=False)
        op.create_index('idx_api_key_hash', 'api_keys', ['api_key_hash'], unique=True)
        op.create_index('idx_api_key_prefix', 'api_keys', ['api_key_prefix'], unique=False)
        
        # Create subscriptions table
        logger.info("Creating subscriptions table...")
        op.create_table('subscriptions',
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('plan_type', sa.Enum('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE', name='subscriptionplan'), nullable=False),
            sa.Column('api_calls_limit', sa.Integer(), nullable=True),
            sa.Column('storage_limit', sa.Integer(), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('expires_at', sa.DateTime(), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('mercadopago_subscription_id', sa.String(length=255), nullable=True),
            sa.Column('mercadopago_price_id', sa.String(length=255), nullable=True),
            sa.Column('payment_gateway', sa.String(length=50), nullable=True, default='mercadopago'),
            sa.Column('monthly_api_calls_used', sa.Integer(), nullable=False, default=0),
            sa.Column('storage_used', sa.BigInteger(), nullable=False, default=0),
            sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True),
            sa.Column('available_models', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('additional_features', postgresql.JSONB(astext_type=sa.Text()), nullable=True, default={}),
            sa.Column('last_successful_training_at', sa.DateTime(timezone=True), nullable=True, comment='Fecha del último entrenamiento exitoso'),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('account_id')
        )
        op.create_index(op.f('ix_subscriptions_mercadopago_subscription_id'), 'subscriptions', ['mercadopago_subscription_id'], unique=False)
        op.create_index(op.f('ix_subscriptions_plan_type'), 'subscriptions', ['plan_type'], unique=False)

        # Create roles table
        logger.info("Creating roles table...")
        op.create_table('roles',
            sa.Column('role_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(length=100), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('role_id'),
            sa.UniqueConstraint('account_id', 'name', name='uq_role_account_name')
        )
        op.create_index('idx_role_account', 'roles', ['account_id'], unique=False)
        op.create_index('idx_role_name', 'roles', ['name'], unique=False)

        # Create permissions table
        logger.info("Creating permissions table...")
        op.create_table('permissions',
            sa.Column('permission_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(length=100), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('resource', sa.String(length=100), nullable=False),
            sa.Column('action', sa.String(length=50), nullable=False),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('permission_id'),
            sa.UniqueConstraint('account_id', 'name', name='uq_permission_account_name')
        )
        op.create_index('idx_permission_account', 'permissions', ['account_id'], unique=False)
        op.create_index('idx_permission_name', 'permissions', ['name'], unique=False)

        # Create role_permissions association table
        logger.info("Creating role_permissions table...")
        op.create_table('role_permissions',
            sa.Column('role_id', sa.Integer(), nullable=False),
            sa.Column('permission_id', sa.Integer(), nullable=False),
            sa.ForeignKeyConstraint(['permission_id'], ['permissions.permission_id'], ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['role_id'], ['roles.role_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('role_id', 'permission_id')
        )

        # Create system_users table
        logger.info("Creating system_users table...")
        op.create_table('system_users',
            sa.Column('user_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('email', sa.String(length=255), nullable=False),
            sa.Column('hashed_password', sa.String(length=255), nullable=False),
            sa.Column('first_name', sa.String(length=100), nullable=True),
            sa.Column('last_name', sa.String(length=100), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('is_verified', sa.Boolean(), nullable=True, default=False),
            sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('user_id')
        )
        op.create_index('idx_system_user_account', 'system_users', ['account_id'], unique=False)
        op.create_index('idx_system_user_email_global', 'system_users', ['email'], unique=True)
        op.create_index('idx_system_users_account_last_login', 'system_users', ['account_id', 'last_login'], unique=False)

        # Create system_user_roles association table
        logger.info("Creating system_user_roles table...")
        op.create_table('system_user_roles',
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('role_id', sa.Integer(), nullable=False),
            sa.ForeignKeyConstraint(['role_id'], ['roles.role_id'], ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['user_id'], ['system_users.user_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('user_id', 'role_id')
        )

        # Create end_users table
        logger.info("Creating end_users table...")
        op.create_table('end_users',
            sa.Column('end_user_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('external_id', sa.String(length=255), nullable=False),
            sa.Column('email', sa.String(length=255), nullable=True),
            sa.Column('first_name', sa.String(length=100), nullable=True),
            sa.Column('last_name', sa.String(length=100), nullable=True),
            sa.Column('date_of_birth', sa.Date(), nullable=True),
            sa.Column('gender', sa.String(length=20), nullable=True),
            sa.Column('location', sa.String(length=255), nullable=True),
            sa.Column('preferred_categories', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('price_range_min', sa.DECIMAL(precision=10, scale=2), nullable=True),
            sa.Column('price_range_max', sa.DECIMAL(precision=10, scale=2), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('end_user_id'),
            sa.UniqueConstraint('account_id', 'external_id', name='uq_end_user_account_external_id')
        )
        op.create_index('idx_end_user_preferred_categories', 'end_users', ['preferred_categories'], unique=False, postgresql_using='gin')
        op.create_index('idx_end_user_price_range', 'end_users', ['price_range_min', 'price_range_max'], unique=False)

        # Create products table
        logger.info("Creating products table...")
        op.create_table('products',
            sa.Column('product_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('external_id', sa.String(length=255), nullable=False),
            sa.Column('name', sa.String(length=255), nullable=False),
            sa.Column('category', sa.String(length=100), nullable=True),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('price', sa.DECIMAL(precision=10, scale=2), nullable=False),
            sa.Column('average_rating', sa.Float(), nullable=True, default=0.0),
            sa.Column('num_ratings', sa.Integer(), nullable=True, default=0),
            sa.Column('inventory_count', sa.Integer(), nullable=True, default=0),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
            sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
            sa.Column('features', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('last_interaction_at', sa.DateTime(timezone=True), nullable=True),
            sa.Column('search_vector', postgresql.TSVECTOR(), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('product_id'),
            sa.UniqueConstraint('account_id', 'external_id', name='uq_product_account_external_id')
        )
        op.create_index('idx_product_account_category', 'products', ['account_id', 'category'], unique=False)
        op.create_index('idx_product_account_name', 'products', ['account_id', 'name'], unique=False)
        op.create_index('idx_product_description_trgm', 'products', ['description'], unique=False, postgresql_using='gin')
        op.create_index('idx_product_name_trgm', 'products', ['name'], unique=False, postgresql_using='gin')
        op.create_index('idx_product_search_vector', 'products', ['search_vector'], unique=False, postgresql_using='gin')
        op.create_index('idx_products_account_last_interaction', 'products', ['account_id', 'last_interaction_at'], unique=False)

        # Create interactions table
        logger.info("Creating interactions table...")
        op.create_table('interactions',
            sa.Column('interaction_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('end_user_id', sa.Integer(), nullable=False),
            sa.Column('product_id', sa.Integer(), nullable=False),
            sa.Column('interaction_type', sa.Enum('VIEW', 'CLICK', 'PURCHASE', 'LIKE', 'DISLIKE', 'ADD_TO_CART', 'REMOVE_FROM_CART', 'SHARE', 'REVIEW', name='interactiontype'), nullable=False),
            sa.Column('rating', sa.Float(), nullable=True),
            sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('session_id', sa.String(length=255), nullable=True),
            sa.Column('context', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['end_user_id'], ['end_users.end_user_id'], ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['product_id'], ['products.product_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('interaction_id')
        )
        op.create_index('idx_interaction_account_timestamp', 'interactions', ['account_id', 'timestamp'], unique=False)
        op.create_index('idx_interaction_end_user_timestamp', 'interactions', ['end_user_id', 'timestamp'], unique=False)
        op.create_index('idx_interaction_product_timestamp', 'interactions', ['product_id', 'timestamp'], unique=False)
        op.create_index('idx_interaction_type', 'interactions', ['interaction_type'], unique=False)

        # Create searches table
        logger.info("Creating searches table...")
        op.create_table('searches',
            sa.Column('search_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('end_user_id', sa.Integer(), nullable=True),
            sa.Column('query', sa.Text(), nullable=False),
            sa.Column('filters', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.Column('results_count', sa.Integer(), nullable=True),
            sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('session_id', sa.String(length=255), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['end_user_id'], ['end_users.end_user_id'], ondelete='SET NULL'),
            sa.PrimaryKeyConstraint('search_id')
        )
        op.create_index('idx_search_account_timestamp', 'searches', ['account_id', 'timestamp'], unique=False)
        op.create_index('idx_search_end_user_timestamp', 'searches', ['end_user_id', 'timestamp'], unique=False)

        # Create notifications table
        logger.info("Creating notifications table...")
        op.create_table('notifications',
            sa.Column('notification_id', sa.Integer(), sa.Identity(always=False, start=1, increment=1), nullable=False),
            sa.Column('account_id', sa.Integer(), nullable=False),
            sa.Column('title', sa.String(length=255), nullable=False),
            sa.Column('message', sa.Text(), nullable=False),
            sa.Column('notification_type', sa.Enum('INFO', 'WARNING', 'ERROR', 'SUCCESS', name='notificationtype'), nullable=False),
            sa.Column('is_read', sa.Boolean(), nullable=True, default=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['account_id'], ['accounts.account_id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('notification_id')
        )
        op.create_index('idx_notification_account', 'notifications', ['account_id'], unique=False)

        logger.info("✅ Consolidated initial migration completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during consolidated migration: {e}")
        raise

def downgrade() -> None:
    """Downgrade schema by removing all tables and extensions."""
    logger.info("Starting consolidated migration rollback...")
    
    try:
        # Drop tables in reverse order
        op.drop_table('subscriptions')
        op.drop_table('api_keys')
        op.drop_table('accounts')
        
        # Drop schema
        op.execute("DROP SCHEMA IF EXISTS app CASCADE;")
        
        # Drop extensions (only if not in use by other objects)
        op.execute("DROP EXTENSION IF EXISTS btree_gin CASCADE;")
        op.execute("DROP EXTENSION IF EXISTS pg_trgm CASCADE;")
        
        logger.info("✅ Consolidated migration rollback completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Error during consolidated migration rollback: {e}")
        raise
