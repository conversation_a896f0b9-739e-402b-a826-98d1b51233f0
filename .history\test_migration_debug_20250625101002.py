#!/usr/bin/env python3
"""
Script para debuggear problemas de migración en Cloud Run.
"""

import os
import sys
import traceback
from datetime import datetime

# Simular el entorno de Cloud Run
os.environ['ENV'] = 'production'
os.environ['GCP_PROJECT_ID'] = 'lateral-insight-461112-g9'
os.environ['PYTHONPATH'] = '/app/src'
os.environ['WORKER_TYPE'] = 'migration'

# Agregar src al path como en el contenedor
sys.path.insert(0, 'rayuela_backend/src')

async def test_migration_environment():
    """Simula el entorno de migración de Cloud Run."""
    
    print(f"🔍 TEST DE ENTORNO DE MIGRACIÓN - {datetime.now()}")
    print("=" * 60)
    
    # 1. Test de importación de configuración
    print("\n1. 🔧 TEST DE CONFIGURACIÓN:")
    try:
        from src.core.config import settings, get_allowed_origins
        print(f"   ✅ Settings importado correctamente")
        print(f"   ✅ get_allowed_origins disponible")
        
        # Test de ALLOWED_ORIGINS
        origins = get_allowed_origins()
        print(f"   ✅ ALLOWED_ORIGINS: {origins}")
        
    except Exception as e:
        print(f"   ❌ Error en configuración: {e}")
        print(f"   🔍 Traceback: {traceback.format_exc()}")
        return False
    
    # 2. Test de importación de modelos
    print("\n2. 📋 TEST DE MODELOS:")
    try:
        import src.db.models
        from src.db.base import Base
        print(f"   ✅ Modelos importados correctamente")
        print(f"   ✅ Tablas en metadata: {len(Base.metadata.tables)}")
        
        # Listar algunas tablas
        tables = list(Base.metadata.tables.keys())[:10]
        print(f"   📋 Primeras tablas: {tables}")
        
    except Exception as e:
        print(f"   ❌ Error importando modelos: {e}")
        print(f"   🔍 Traceback: {traceback.format_exc()}")
        return False
    
    # 3. Test de Alembic
    print("\n3. 🔄 TEST DE ALEMBIC:")
    try:
        from alembic.config import Config
        from alembic import command
        
        # Crear configuración de Alembic
        alembic_cfg = Config("rayuela_backend/alembic.ini")
        
        print(f"   ✅ Configuración de Alembic cargada")
        
        # Test de current revision (sin conectar a BD)
        print(f"   ✅ Alembic configurado correctamente")
        
    except Exception as e:
        print(f"   ❌ Error en Alembic: {e}")
        print(f"   🔍 Traceback: {traceback.format_exc()}")
        return False
    
    print("\n🎉 TODOS LOS TESTS PASARON")
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(test_migration_environment())
        if success:
            print("✅ Entorno de migración OK")
            sys.exit(0)
        else:
            print("❌ Problemas en entorno de migración")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error fatal: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)
