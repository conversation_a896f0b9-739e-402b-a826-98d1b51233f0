#!/usr/bin/env python3
"""
Script de diagnóstico para migraciones de base de datos
Ejecuta verificaciones detalladas antes de aplicar migraciones
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import settings
from src.core.database import DatabaseManager
from src.core.cache import CacheManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test database connection and basic operations."""
    logger.info("🔍 Testing database connection...")
    
    try:
        db_manager = DatabaseManager()
        
        # Test connection
        async with db_manager.get_session() as session:
            result = await session.execute("SELECT version();")
            version = result.scalar()
            logger.info(f"✅ Database connected successfully: {version}")
            
            # Test basic query
            result = await session.execute("SELECT current_database(), current_user;")
            db_info = result.fetchone()
            logger.info(f"✅ Database: {db_info[0]}, User: {db_info[1]}")
            
            # Check if alembic_version table exists
            result = await session.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'alembic_version'
                );
            """)
            has_alembic = result.scalar()
            logger.info(f"✅ Alembic version table exists: {has_alembic}")
            
            if has_alembic:
                result = await session.execute("SELECT version_num FROM alembic_version;")
                current_version = result.scalar()
                logger.info(f"✅ Current migration version: {current_version}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

async def test_cache_connection():
    """Test Redis cache connection."""
    logger.info("🔍 Testing Redis cache connection...")
    
    try:
        cache_manager = CacheManager()
        
        # Test basic operations
        await cache_manager.set("test_key", "test_value", ttl=60)
        value = await cache_manager.get("test_key")
        
        if value == "test_value":
            logger.info("✅ Redis cache connected successfully")
            await cache_manager.delete("test_key")
            return True
        else:
            logger.error("❌ Redis cache test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis cache connection failed: {e}")
        return False

def check_environment_variables():
    """Check critical environment variables."""
    logger.info("🔍 Checking environment variables...")
    
    critical_vars = [
        "POSTGRES_HOST",
        "POSTGRES_PORT", 
        "POSTGRES_DB",
        "POSTGRES_USER",
        "POSTGRES_PASSWORD",
        "REDIS_URL"
    ]
    
    missing_vars = []
    for var in critical_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "PASSWORD" in var or "SECRET" in var:
                display_value = "*" * len(value)
            else:
                display_value = value
            logger.info(f"✅ {var}: {display_value}")
        else:
            missing_vars.append(var)
            logger.error(f"❌ {var}: NOT SET")
    
    if missing_vars:
        logger.error(f"❌ Missing critical environment variables: {missing_vars}")
        return False
    
    return True

async def run_migration_check():
    """Run migration check using Alembic."""
    logger.info("🔍 Running Alembic migration check...")
    
    try:
        import subprocess
        
        # Check current revision
        result = subprocess.run(
            ["alembic", "current"],
            capture_output=True,
            text=True,
            cwd="/app"
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Current revision: {result.stdout.strip()}")
        else:
            logger.error(f"❌ Alembic current failed: {result.stderr}")
            return False
        
        # Check pending migrations
        result = subprocess.run(
            ["alembic", "heads"],
            capture_output=True,
            text=True,
            cwd="/app"
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Available heads: {result.stdout.strip()}")
        else:
            logger.error(f"❌ Alembic heads failed: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration check failed: {e}")
        return False

async def main():
    """Main diagnostic function."""
    logger.info("🚀 Starting migration diagnostics...")
    
    # Check environment
    env_ok = check_environment_variables()
    
    # Test connections
    db_ok = await test_database_connection()
    cache_ok = await test_cache_connection()
    
    # Check migrations
    migration_ok = await run_migration_check()
    
    # Summary
    logger.info("📊 DIAGNOSTIC SUMMARY:")
    logger.info(f"   Environment Variables: {'✅' if env_ok else '❌'}")
    logger.info(f"   Database Connection: {'✅' if db_ok else '❌'}")
    logger.info(f"   Redis Cache: {'✅' if cache_ok else '❌'}")
    logger.info(f"   Migration Check: {'✅' if migration_ok else '❌'}")
    
    if all([env_ok, db_ok, cache_ok, migration_ok]):
        logger.info("🎉 All diagnostics passed! Ready for migration.")
        return 0
    else:
        logger.error("❌ Some diagnostics failed. Check logs above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
