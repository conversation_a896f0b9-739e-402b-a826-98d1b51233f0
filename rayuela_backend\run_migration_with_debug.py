#!/usr/bin/env python3
"""
Script de migración con diagnósticos detallados
Ejecuta diagnósticos completos antes de aplicar migraciones
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """Run a command and log the results."""
    logger.info(f"🔧 {description}")
    logger.info(f"   Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd="/app"
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} - SUCCESS")
            if result.stdout.strip():
                logger.info(f"   Output: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"❌ {description} - FAILED")
            logger.error(f"   Return code: {result.returncode}")
            if result.stderr.strip():
                logger.error(f"   Error: {result.stderr.strip()}")
            if result.stdout.strip():
                logger.error(f"   Output: {result.stdout.strip()}")
            return False
            
    except Exception as e:
        logger.error(f"❌ {description} - EXCEPTION: {e}")
        return False

async def run_diagnostics():
    """Run comprehensive diagnostics."""
    logger.info("🚀 Running comprehensive diagnostics...")
    
    try:
        # Import and run the debug script
        from debug_migration import main as debug_main
        result = await debug_main()
        return result == 0
    except Exception as e:
        logger.error(f"❌ Diagnostics failed: {e}")
        return False

def check_alembic_setup():
    """Check Alembic configuration and status."""
    logger.info("🔍 Checking Alembic setup...")
    
    # Check if alembic.ini exists
    alembic_ini = Path("/app/alembic.ini")
    if not alembic_ini.exists():
        logger.error("❌ alembic.ini not found")
        return False
    
    logger.info("✅ alembic.ini found")
    
    # Check versions directory
    versions_dir = Path("/app/alembic/versions")
    if not versions_dir.exists():
        logger.error("❌ alembic/versions directory not found")
        return False
    
    logger.info("✅ alembic/versions directory found")
    
    # List migration files
    migration_files = list(versions_dir.glob("*.py"))
    logger.info(f"✅ Found {len(migration_files)} migration files:")
    for file in migration_files:
        logger.info(f"   - {file.name}")
    
    return True

def run_alembic_migrations():
    """Run Alembic migrations with detailed logging."""
    logger.info("🔄 Starting Alembic migrations...")
    
    # Step 1: Check current revision
    if not run_command(["alembic", "current"], "Check current revision"):
        return False
    
    # Step 2: Show migration history
    if not run_command(["alembic", "history"], "Show migration history"):
        logger.warning("⚠️ Could not show migration history, continuing...")
    
    # Step 3: Check what migrations are pending
    if not run_command(["alembic", "heads"], "Check available heads"):
        return False
    
    # Step 4: Run migrations
    logger.info("🚀 Applying migrations...")
    if not run_command(["alembic", "upgrade", "head"], "Apply migrations"):
        return False
    
    # Step 5: Verify final state
    if not run_command(["alembic", "current"], "Verify final revision"):
        return False
    
    logger.info("✅ All migrations completed successfully!")
    return True

async def main():
    """Main migration function with comprehensive checks."""
    logger.info("🚀 Starting migration process with diagnostics...")
    
    # Step 1: Check Alembic setup
    if not check_alembic_setup():
        logger.error("❌ Alembic setup check failed")
        return 1
    
    # Step 2: Run diagnostics
    if not await run_diagnostics():
        logger.error("❌ Diagnostics failed")
        return 1
    
    # Step 3: Run migrations
    if not run_alembic_migrations():
        logger.error("❌ Migration failed")
        return 1
    
    logger.info("🎉 Migration process completed successfully!")
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
