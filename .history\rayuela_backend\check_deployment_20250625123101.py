#!/usr/bin/env python3
"""
Script de diagnóstico para verificar la configuración del despliegue.
Se usa para debugging de problemas de conectividad en Cloud Run.
"""

import os
import sys
import asyncio
import asyncpg
import traceback
from datetime import datetime

# Agregar src al path
sys.path.insert(0, '/app/src')

async def check_deployment():
    """Ejecuta verificaciones básicas de despliegue."""
    
    print(f"🔍 DIAGNÓSTICO DE DESPLIEGUE - {datetime.now()}")
    print("=" * 50)
    
    # 1. Verificar variables de entorno críticas
    print("\n1. ✅ VARIABLES DE ENTORNO:")
    env_vars = [
        'ENV', 'GCP_PROJECT_ID', 'POSTGRES_SERVER', 'POSTGRES_DB', 
        'POSTGRES_USER', 'POSTGRES_PORT', 'WORKER_TYPE'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'NOT SET')
        if var == 'POSTGRES_PASSWORD':
            print(f"   {var}: {'***SET***' if value != 'NOT SET' else 'NOT SET'}")
        else:
            print(f"   {var}: {value}")
    
    # 2. Verificar si los secretos están disponibles
    print("\n2. 🔐 VERIFICACIÓN DE SECRETOS:")
    try:
        from src.core.config import settings
        print(f"   SECRET_KEY: {'***SET***' if settings.SECRET_KEY else 'NOT SET'}")
        print(f"   POSTGRES_PASSWORD: {'***SET***' if settings.POSTGRES_PASSWORD else 'NOT SET'}")
        print(f"   Database URL: postgresql://***:***@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")
    except Exception as e:
        print(f"   ❌ Error cargando configuración: {e}")
        return False
    
    # 3. Verificar conectividad de base de datos
    print("\n3. 🗄️ VERIFICACIÓN DE BASE DE DATOS:")
    try:
        # Usar parámetros de conexión individuales para evitar problemas de parsing de URL
        conn_params = settings.database_connection_params

        print(f"   Conectando a: {settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}")

        # Intentar conexión directa con asyncpg usando parámetros individuales
        conn = await asyncio.wait_for(
            asyncpg.connect(**conn_params),
            timeout=30
        )
        
        # Ejecutar una consulta simple
        result = await conn.fetchval("SELECT 1")
        print(f"   ✅ Conexión exitosa - Test query result: {result}")
        
        # Verificar si la tabla alembic_version existe
        try:
            version = await conn.fetchval("SELECT version_num FROM alembic_version")
            print(f"   ✅ Migración actual: {version}")
        except:
            print("   ⚠️ Tabla alembic_version no encontrada - Primera migración")
        
        await conn.close()
        print("   ✅ Conexión cerrada correctamente")
        
    except asyncio.TimeoutError:
        print("   ❌ Timeout de conexión (30s)")
        return False
    except Exception as e:
        print(f"   ❌ Error de conexión: {e}")
        print(f"   🔍 Traceback: {traceback.format_exc()}")
        return False
    
    # 4. Verificar importación de modelos
    print("\n4. 📋 VERIFICACIÓN DE MODELOS:")
    try:
        import src.db.models
        from src.db.base import Base
        print(f"   ✅ Modelos importados correctamente")
        print(f"   ✅ Tablas en metadata: {len(Base.metadata.tables)}")
        
        # Listar algunas tablas
        tables = list(Base.metadata.tables.keys())[:5]
        print(f"   📋 Primeras tablas: {tables}")
        
    except Exception as e:
        print(f"   ❌ Error importando modelos: {e}")
        return False
    
    print("\n🎉 DIAGNÓSTICO COMPLETADO EXITOSAMENTE")
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(check_deployment())
        if success:
            print("✅ Todos los checks pasaron - Sistema listo")
            sys.exit(0)
        else:
            print("❌ Algunos checks fallaron - Revisar configuración")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error fatal en diagnóstico: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1) 