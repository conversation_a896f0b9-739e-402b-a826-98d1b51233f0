/**
 * Shared utilities for chart components
 * This file contains common functions, options, and styles for charts
 */

import { ChartOptions } from 'chart.js';

// Standard color palette for consistency across charts
export const chartColors = {
  blue: {
    primary: 'rgb(59, 130, 246)',
    background: 'rgba(59, 130, 246, 0.5)',
  },
  green: {
    primary: 'rgb(16, 185, 129)',
    background: 'rgba(16, 185, 129, 0.5)',
  },
  orange: {
    primary: 'rgb(245, 158, 11)',
    background: 'rgba(245, 158, 11, 0.5)',
  },
  purple: {
    primary: 'rgb(139, 92, 246)',
    background: 'rgba(139, 92, 246, 0.5)',
  },
  red: {
    primary: 'rgb(239, 68, 68)',
    background: 'rgba(239, 68, 68, 0.5)',
  },
  // Add more colors as needed
};

// Get a color set by index (for multiple datasets)
export const getColorByIndex = (index: number) => {
  const colors = [
    chartColors.blue,
    chartColors.green,
    chartColors.orange,
    chartColors.purple,
    chartColors.red,
    // Add fallback colors for more datasets
    { primary: `rgb(${59 + index * 50}, ${130 + index * 30}, ${246 - index * 40})`, 
      background: `rgba(${59 + index * 50}, ${130 + index * 30}, ${246 - index * 40}, 0.5)` },
  ];
  
  return colors[index % colors.length];
};

// Common chart options that can be extended for specific chart types
export const baseChartOptions: ChartOptions<'line' | 'bar' | 'radar' | 'doughnut' | 'pie'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        boxWidth: 12,
        padding: 15,
        font: {
          size: 11
        }
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      titleFont: {
        size: 12,
        weight: 'bold'
      },
      bodyFont: {
        size: 11
      },
      padding: 8,
      cornerRadius: 4,
      displayColors: true,
      boxWidth: 8,
      boxHeight: 8,
      boxPadding: 4,
      usePointStyle: true,
    }
  },
  scales: {
    x: {
      grid: {
        display: false,
      },
      ticks: {
        font: {
          size: 10
        },
        maxRotation: 45,
        minRotation: 45
      }
    },
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        font: {
          size: 11
        }
      }
    }
  },
};

// Helper function to format bytes to human-readable format
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Helper function to format numbers with thousands separators
export function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

// Helper function to format percentages
export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`;
}

// Common tooltip callbacks for different data types
export const tooltipCallbacks = {
  // For percentage values
  percentage: {
    label: (context: { dataset: { label?: string }; parsed: { y: number | null } }) => {
      let label = context.dataset.label || '';
      if (label) {
        label += ': ';
      }
      if (context.parsed.y !== null) {
        label += formatPercentage(context.parsed.y);
      }
      return label;
    }
  },

  // For byte values
  bytes: {
    label: (context: { dataset: { label?: string }; parsed: { y: number | null } }) => {
      let label = context.dataset.label || '';
      if (label) {
        label += ': ';
      }
      if (context.parsed.y !== null) {
        label += formatBytes(context.parsed.y);
      }
      return label;
    }
  },

  // For numeric values
  number: {
    label: (context: { dataset: { label?: string }; parsed: { y: number | null } }) => {
      let label = context.dataset.label || '';
      if (label) {
        label += ': ';
      }
      if (context.parsed.y !== null) {
        label += formatNumber(context.parsed.y);
      }
      return label;
    }
  }
};

// Helper function to transform API data for usage charts
export interface UsageDataPoint {
  date: string;
  apiCalls: number;
  storage: number;
}

export function transformUsageData(data: any[] = []): UsageDataPoint[] {
  if (!data || data.length === 0) {
    return generateDemoUsageData();
  }
  
  return data.map(item => ({
    date: item.date,
    apiCalls: item.api_calls,
    storage: item.storage
  }));
}

// Generate demo data for preview when no real data is available
export function generateDemoUsageData(): UsageDataPoint[] {
  const demoData: UsageDataPoint[] = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    demoData.push({
      date: date.toISOString().split('T')[0],
      apiCalls: Math.floor(Math.random() * 500) + 100,
      storage: Math.floor(Math.random() * 10000000) + 1000000, // Bytes
    });
  }

  return demoData;
}
